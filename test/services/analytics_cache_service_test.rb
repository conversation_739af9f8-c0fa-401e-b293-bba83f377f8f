require "test_helper"

class AnalyticsCacheServiceTest < ActiveSupport::TestCase
  setup do
    @user = users(:one)
    @start_date = 7.days.ago.to_date
    @end_date = Date.current
    @service = AnalyticsCacheService.new(
      user: @user,
      start_date: @start_date,
      end_date: @end_date
    )
  end

  test "calculates overview metrics without random data" do
    metrics = @service.overview_metrics

    assert_not_nil metrics
    assert metrics[:total_mentions].is_a?(Integer)
    assert metrics[:total_leads].is_a?(Integer)
    assert metrics[:conversion_rate].is_a?(Numeric)
    assert metrics[:average_score].is_a?(Numeric)

    # Ensure no random values
    refute metrics.values.any? { |v| v.nil? && v != 0 }
  end

  test "generates performance data from actual database records" do
    performance = @service.performance_data

    assert_not_nil performance
    assert_not_nil performance[:mentions_trend]
    assert_not_nil performance[:leads_trend]

    # Check that trends have proper structure
    assert performance[:mentions_trend][:current].is_a?(Integer)
    assert performance[:mentions_trend][:previous].is_a?(Integer)
    assert %w[up down neutral].include?(performance[:mentions_trend][:direction])
  end

  test "conversion funnel uses real data" do
    funnel = @service.conversion_funnel

    assert_not_nil funnel
    assert_not_nil funnel[:stages]
    assert funnel[:stages].is_a?(Array)

    # Check funnel stages are properly calculated
    funnel[:stages].each do |stage|
      assert stage[:count].is_a?(Integer)
      assert stage[:percentage].is_a?(Numeric)
      assert stage[:percentage] <= 100
    end
  end

  test "top performers returns actual records or empty arrays" do
    performers = @service.top_performers

    assert_not_nil performers
    assert performers[:keywords].is_a?(Array)
    assert performers[:leads].is_a?(Array)
    assert performers[:integrations].is_a?(Array)
    assert performers[:conversion_sources].is_a?(Array)

    # Ensure no nil values in results
    performers.values.each do |array|
      refute array.any?(&:nil?)
    end
  end

  test "caching works correctly with Rails.cache.fetch" do
    # First call should hit the database
    metrics1 = @service.overview_metrics

    # Second call should use cache
    metrics2 = @service.overview_metrics

    assert_equal metrics1, metrics2

    # Clear cache
    @service.clear_cache

    # Next call should hit database again
    metrics3 = @service.overview_metrics
    assert_equal metrics1, metrics3
  end

  test "handles missing data gracefully" do
    # Create service for date range with no data
    empty_service = AnalyticsCacheService.new(
      user: @user,
      start_date: 1.year.from_now.to_date,
      end_date: 1.year.from_now.to_date + 1.day
    )

    metrics = empty_service.overview_metrics

    assert_equal 0, metrics[:total_mentions]
    assert_equal 0, metrics[:total_leads]
    assert_equal 0, metrics[:conversion_rate]
    assert_equal 0, metrics[:average_score]
  end
end