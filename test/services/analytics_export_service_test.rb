require "test_helper"
require "ostruct"

class AnalyticsExportServiceTest < ActiveSupport::TestCase
  test "sanitizes CSV injection values" do
    data = {
      overview: {
        "=cmd|'/c calc'!A1": { current: "=1+1", change_percentage: "+10", previous: "-5" }
      }
    }

    service = AnalyticsExportService.new(export_type: "overview", data: data)
    csv = service.generate_csv

    # Check that dangerous characters are escaped with single quotes
    # Note: CSV may convert to lowercase
    assert_includes csv.downcase, "'=cmd|'/c calc'!a1"
    assert_includes csv, "'=1+1"
    assert_includes csv, "'+10"
    assert_includes csv, "'-5"
  end

  test "handles nil values gracefully" do
    data = {
      keywords: [
        {
          keyword: nil,
          platform: "Twitter",
          mentions_count: nil,
          leads_count: 5,
          conversion_rate: nil,
          average_score: 0
        }
      ]
    }

    service = AnalyticsExportService.new(export_type: "keywords", data: data)
    csv = service.generate_csv

    assert_not_nil csv
    assert_includes csv, "Twitter"
    assert_includes csv, "5"
  end

  test "sanitizes @ symbol for formula injection" do
    data = {
      leads: [
        OpenStruct.new(
          id: 1,
          author_name: "@malicious",
          email: "@<EMAIL>",
          company: "Test Co",
          score: 85,
          status: "new",
          mention: OpenStruct.new(platform: "Twitter"),
          created_at: Time.current
        )
      ]
    }

    service = AnalyticsExportService.new(export_type: "leads", data: data)
    csv = service.generate_csv

    assert_includes csv, "'@malicious"
    assert_includes csv, "'@<EMAIL>"
  end

  test "handles tab characters in values" do
    data = {
      keywords: [
        {
          keyword: "\tmalicious",
          platform: "Reddit",
          mentions_count: 10,
          leads_count: 2,
          conversion_rate: 20,
          average_score: 75
        }
      ]
    }

    service = AnalyticsExportService.new(export_type: "keywords", data: data)
    csv = service.generate_csv

    assert_includes csv, "'\tmalicious"
  end
end