# Phase 5: Advanced AI & Intelligence Features

## 🎯 Objective  
Implement cutting-edge AI features for competitive differentiation and enhanced user value.

## 📅 Timeline: 3-4 Weeks

## ✅ Implementation Checklist

### Week 1: Enhanced AI Analysis

#### Sentiment Analysis
- [ ] Implement sentiment scoring
- [ ] Add emotion detection
- [ ] Create urgency indicators
- [ ] Build intent classification
- [ ] Add language detection
- [ ] Implement sarcasm detection

#### Topic Modeling
- [ ] Extract key topics
- [ ] Identify trending themes
- [ ] Create topic clusters
- [ ] Build knowledge graph
- [ ] Add entity recognition
- [ ] Implement relationship mapping

### Week 2: Machine Learning Features

#### Lead Scoring ML
- [ ] Create training dataset
- [ ] Build scoring model
- [ ] Implement feature engineering
- [ ] Add real-time scoring
- [ ] Create feedback loop
- [ ] Build A/B testing framework

#### Predictive Analytics
- [ ] Conversion prediction
- [ ] Churn prediction
- [ ] Trend forecasting
- [ ] Opportunity detection
- [ ] Anomaly detection
- [ ] Performance prediction

### Week 3: Intelligent Automation

#### Smart Responses
- [ ] Response template generation
- [ ] Personalization engine
- [ ] Tone matching
- [ ] Context awareness
- [ ] Multi-language support
- [ ] Response optimization

#### Content Intelligence
- [ ] Auto-categorization
- [ ] Smart tagging
- [ ] Duplicate detection
- [ ] Content summarization
- [ ] Insight extraction
- [ ] Recommendation engine

### Week 4: Advanced Search & Discovery

#### Elasticsearch Integration
- [ ] Full-text search setup
- [ ] Faceted search implementation
- [ ] Search relevance tuning
- [ ] Autocomplete/suggestions
- [ ] Search analytics
- [ ] Saved searches

#### Recommendation System
- [ ] Similar leads discovery
- [ ] Keyword suggestions
- [ ] Content recommendations
- [ ] Strategy suggestions
- [ ] Integration recommendations
- [ ] Workflow optimization

## 🔧 Technical Implementation

### 1. Enhanced AI Analysis Service
```ruby
# app/services/ai_analysis_service.rb
class AiAnalysisService
  def initialize(mention)
    @mention = mention
def initialize(mention)
  @mention = mention
  @client = OpenAI::Client.new(access_token: api_key)
end

private

def api_key
  Rails.application.credentials.dig(:openai, :api_key) ||
    ENV['OPENAI_API_KEY'] ||
    raise('OpenAI API key not configured')
end
  end
  
  def comprehensive_analysis
    {
      sentiment: analyze_sentiment,
      entities: extract_entities,
      topics: extract_topics,
      intent: classify_intent,
      urgency: calculate_urgency,
      lead_score: calculate_lead_score,
      suggested_response: generate_response,
      insights: generate_insights
    }
  end
  
  private
  
  def analyze_sentiment
def analyze_sentiment
  retries = 0
  begin
    response = @client.completions(
      parameters: {
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are a sentiment analysis expert. Analyze the sentiment and provide a detailed breakdown."
          },
          {
            role: "user",
            content: sentiment_prompt
          }
        ],
        max_tokens: 200,
        temperature: 0.3
      }
    )

    parse_sentiment_response(response)
  rescue OpenAI::RateLimitError => e
    retries += 1
    if retries <= 3
      sleep(2**retries)
      retry
    else
      Rails.logger.error("OpenAI rate limit exceeded: #{e.message}")
      fallback_sentiment_analysis
    end
  rescue StandardError => e
    Rails.logger.error("OpenAI API error: #{e.message}")
    fallback_sentiment_analysis
  end
end

def fallback_sentiment_analysis
  # Simple rule-based fallback
  {
    overall:   'neutral',
    confidence: 0.5,
    error:      'AI analysis unavailable, using fallback'
  }
end
  
  def sentiment_prompt
    <<~PROMPT
      Analyze the sentiment of this social media mention:
      
      Content: #{@mention.content}
      Author: #{@mention.author}
      Platform: #{@mention.platform}
      
      Provide:
      1. Overall sentiment (positive/negative/neutral) with confidence score
      2. Emotional tone (excited, frustrated, curious, urgent, etc.)
      3. Intent (seeking help, praising, complaining, asking question)
      4. Urgency level (1-10)
      5. Purchase intent likelihood (1-10)
      
      Format as JSON.
    PROMPT
  end
  
  def extract_entities
    response = @client.completions(
      parameters: {
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "Extract named entities and their relationships from text."
          },
          {
            role: "user",
            content: entity_extraction_prompt
          }
        ],
        max_tokens: 150
      }
    )
    
    parse_entity_response(response)
  end
  
  def entity_extraction_prompt
    <<~PROMPT
      Extract entities from this text:
      #{@mention.content}
      
      Identify:
      - People/Organizations
      - Products/Services mentioned
      - Locations
      - Technologies/Tools
      - Competitors mentioned
      - Pain points/Problems
      - Desired outcomes
      
      Format as JSON with entity type and value.
    PROMPT
  end
  
  def classify_intent
    intents = {
      purchase_inquiry: detect_purchase_intent,
      support_request: detect_support_need,
      feature_request: detect_feature_request,
      competitor_comparison: detect_competitor_mention,
      general_question: detect_question,
      feedback: detect_feedback
    }
    
    intents.select { |_, score| score > 0.3 }
           .sort_by { |_, score| -score }
           .first(3)
           .to_h
  end
  
  def calculate_urgency
    factors = {
      sentiment_negativity: sentiment_score[:negative] * 0.3,
      question_marks: (@mention.content.count('?') / 10.0) * 0.2,
      urgency_keywords: urgency_keyword_score * 0.3,
      caps_usage: caps_percentage * 0.1,
      exclamation_marks: (@mention.content.count('!') / 10.0) * 0.1
    }
    
    (factors.values.sum * 10).round(1).clamp(0, 10)
  end
  
  def calculate_lead_score
    LeadScoringService.new(@mention).calculate
  end
  
  def generate_response
    ResponseGenerationService.new(@mention, analysis_context).generate
  end
  
  def generate_insights
    insights = []
    
    # Sentiment insights
    if sentiment_score[:negative] > 0.7
      insights << {
        type: 'urgent',
        message: 'High negative sentiment detected - immediate response recommended',
        action: 'respond_immediately'
      }
    end
    
    # Intent insights
    if intents[:purchase_inquiry] > 0.6
      insights << {
        type: 'opportunity',
        message: 'Strong purchase intent detected',
        action: 'sales_followup'
      }
    end
    
    # Competitor insights
    if entities[:competitors].any?
      insights << {
        type: 'competitive',
        message: "Competitor mentioned: #{entities[:competitors].join(', ')}",
        action: 'competitive_positioning'
      }
    end
    
    insights
  end
  
  def urgency_keyword_score
    urgent_keywords = %w[urgent asap immediately now today help need must critical important]
    matches = urgent_keywords.count { |word| @mention.content.downcase.include?(word) }
    (matches / urgent_keywords.length.to_f)
  end
  
  def caps_percentage
    return 0 if @mention.content.empty?
    caps_count = @mention.content.scan(/[A-Z]/).length
    (caps_count.to_f / @mention.content.length).clamp(0, 1)
  end
end
```

### 2. Machine Learning Lead Scoring
```ruby
# app/services/lead_scoring_service.rb
class LeadScoringService
  def initialize(mention_or_lead)
    @object = mention_or_lead
    @features = extract_features
  end
  
  def calculate
    # Use pre-trained model or rule-based scoring
    if ml_model_available?
      ml_score
    else
      rule_based_score
    end
  end
  
  private
  
  def extract_features
    {
      # Author features
      author_followers: @object.author_followers || 0,
      author_engagement_rate: calculate_engagement_rate,
      author_verified: @object.author_verified? ? 1 : 0,
      author_bio_relevance: calculate_bio_relevance,
      
      # Content features
      sentiment_score: @object.sentiment_score || 0,
      intent_strength: @object.intent_scores&.values&.max || 0,
      keyword_relevance: calculate_keyword_relevance,
      urgency_level: @object.urgency_score || 0,
      
      # Engagement features
      likes_count: @object.likes_count || 0,
      replies_count: @object.replies_count || 0,
      shares_count: @object.shares_count || 0,
      engagement_velocity: calculate_engagement_velocity,
      
      # Context features
      platform_weight: platform_weights[@object.platform] || 0.5,
      time_of_day_score: time_of_day_score,
      day_of_week_score: day_of_week_score,
      competitive_mention: @object.has_competitor_mention? ? 1 : 0,
      
      # Historical features
      previous_interactions: count_previous_interactions,
      conversion_likelihood: historical_conversion_rate
    }
  end
  
  def ml_score
    # Load pre-trained model
    model = load_ml_model
    
    # Prepare features for model
    feature_vector = prepare_feature_vector(@features)
    
    # Get prediction
    score = model.predict(feature_vector)
    
    # Apply calibration
    calibrated_score = calibrate_score(score)
    
    (calibrated_score * 100).round
  end
  
  def rule_based_score
    weights = {
      author_followers: 0.15,
      author_engagement_rate: 0.10,
      sentiment_score: 0.15,
      intent_strength: 0.20,
      keyword_relevance: 0.15,
      urgency_level: 0.10,
      engagement_velocity: 0.10,
      competitive_mention: 0.05
    }
    
    score = weights.sum do |feature, weight|
      normalized_value = normalize_feature(feature, @features[feature])
      normalized_value * weight
    end
    
    (score * 100).round.clamp(0, 100)
  end
  
  def normalize_feature(feature, value)
    case feature
    when :author_followers
      Math.log10(value + 1) / 6.0  # Normalize assuming max 1M followers
    when :author_engagement_rate
      value.clamp(0, 1)
    when :sentiment_score
      (value + 1) / 2.0  # Convert from [-1, 1] to [0, 1]
    else
      value.to_f.clamp(0, 1)
    end
  end
  
  def calculate_engagement_rate
    return 0 if @object.author_followers.to_i.zero?
    
    total_engagement = (@object.likes_count + @object.replies_count + @object.shares_count)
    (total_engagement.to_f / @object.author_followers).clamp(0, 1)
  end
  
  def calculate_keyword_relevance
    return 0 unless @object.respond_to?(:keyword)
    
    keyword_terms = @object.keyword.keyword.downcase.split
    content_terms = @object.content.downcase.split
    
    matches = keyword_terms.count { |term| content_terms.include?(term) }
    (matches.to_f / keyword_terms.length).clamp(0, 1)
  end
  
  def calculate_engagement_velocity
    return 0 unless @object.created_at
    
    hours_since_creation = (Time.current - @object.created_at) / 1.hour
    return 1 if hours_since_creation < 1
    
    total_engagement = @object.likes_count + @object.replies_count + @object.shares_count
    velocity = total_engagement / hours_since_creation
    
    # Normalize based on expected max velocity
    (velocity / 100.0).clamp(0, 1)
  end
  
  def platform_weights
    {
      'twitter' => 0.7,
      'linkedin' => 0.9,
      'facebook' => 0.6,
      'reddit' => 0.8,
      'instagram' => 0.5
    }
  end
  
  def time_of_day_score
    hour = @object.created_at&.hour || Time.current.hour
    
    # Business hours score higher
    case hour
    when 9..11, 14..16
      1.0  # Peak business hours
    when 8, 12..13, 17
      0.8  # Regular business hours
    when 7, 18..19
      0.6  # Extended hours
    else
      0.3  # Off hours
    end
  end
  
  def day_of_week_score
    day = @object.created_at&.wday || Time.current.wday
    
    case day
    when 2..4  # Tuesday to Thursday
      1.0
    when 1, 5  # Monday, Friday
      0.8
    else  # Weekend
      0.5
    end
  end
  
  def count_previous_interactions
    return 0 unless @object.respond_to?(:author)
    
    Mention.where(author: @object.author)
           .where('created_at < ?', @object.created_at)
           .count
  end
  
  def historical_conversion_rate
    return 0.5 unless @object.respond_to?(:keyword)
    
    @object.keyword.conversion_rate / 100.0
  end
  
  def ml_model_available?
    File.exist?(Rails.root.join('models', 'lead_scoring_model.pkl'))
  end
  
  def load_ml_model
    # Load serialized model (would use ONNX or similar in production)
    model_path = Rails.root.join('models', 'lead_scoring_model.pkl')
    Marshal.load(File.read(model_path))
  end
  
  def prepare_feature_vector(features)
    # Convert features hash to array in correct order for model
    feature_order = %i[
      author_followers author_engagement_rate author_verified
      sentiment_score intent_strength keyword_relevance urgency_level
      likes_count replies_count shares_count engagement_velocity
      platform_weight time_of_day_score day_of_week_score
      competitive_mention previous_interactions conversion_likelihood
    ]
    
    feature_order.map { |f| features[f] || 0 }
  end
  
  def calibrate_score(raw_score)
    # Apply isotonic regression or Platt scaling for calibration
    # This ensures scores are well-calibrated probabilities
    raw_score.clamp(0, 1)
  end
end
```

### 3. Smart Response Generation
```ruby
# app/services/response_generation_service.rb
class ResponseGenerationService
  def initialize(mention, context = {})
    @mention = mention
    @context = context
    @client = OpenAI::Client.new(access_token: Rails.application.credentials.openai_api_key)
  end
  
  def generate
    {
      suggested_responses: generate_response_options,
      recommended_response: select_best_response,
      personalization: personalization_suggestions,
      follow_up_strategy: generate_follow_up_plan
    }
  end
  
  private
  
  def generate_response_options
    tones = determine_appropriate_tones
    
    tones.map do |tone|
      {
        tone: tone,
        response: generate_response_with_tone(tone),
        effectiveness_score: predict_effectiveness(tone)
      }
    end.sort_by { |r| -r[:effectiveness_score] }
  end
  
  def determine_appropriate_tones
    base_tones = ['professional', 'friendly', 'empathetic']
    
    # Add context-specific tones
    base_tones << 'apologetic' if @context[:sentiment] == 'negative'
    base_tones << 'enthusiastic' if @context[:intent] == 'purchase_inquiry'
    base_tones << 'helpful' if @context[:intent] == 'support_request'
    
    base_tones
  end
  
  def generate_response_with_tone(tone)
    response = @client.completions(
      parameters: {
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: system_prompt(tone)
          },
          {
            role: "user",
            content: user_prompt
          }
        ],
        max_tokens: 150,
        temperature: tone_temperature(tone)
      }
    )
    
    extract_response(response)
  end
  
  def system_prompt(tone)
    <<~PROMPT
      You are a social media manager responding to customer mentions.
      Generate a #{tone} response that:
      - Addresses the customer's concern/question
      - Maintains brand voice
      - Encourages further engagement
      - Is concise and appropriate for #{@mention.platform}
      
      Brand guidelines:
      - Be helpful and human
      - Show genuine interest in solving problems
      - Avoid corporate jargon
      - Include a clear next step when appropriate
    PROMPT
  end
  
  def user_prompt
    <<~PROMPT
      Customer mention:
      Author: #{@mention.author}
      Content: #{@mention.content}
      Platform: #{@mention.platform}
      Sentiment: #{@context[:sentiment]}
      Intent: #{@context[:intent]}
      
      Generate an appropriate response.
    PROMPT
  end
  
  def tone_temperature(tone)
    {
      'professional' => 0.3,
      'friendly' => 0.5,
      'empathetic' => 0.4,
      'enthusiastic' => 0.6,
      'apologetic' => 0.3,
      'helpful' => 0.4
    }.fetch(tone, 0.4)
  end
  
  def predict_effectiveness(tone)
    # Use historical data to predict response effectiveness
    historical_success = calculate_historical_success(tone)
    context_match = calculate_context_match(tone)
    platform_appropriateness = calculate_platform_fit(tone)
    
    (historical_success * 0.4 + context_match * 0.4 + platform_appropriateness * 0.2)
  end
  
  def select_best_response
    responses = generate_response_options
    responses.first
  end
  
  def personalization_suggestions
    {
      use_name: should_use_name?,
      reference_history: previous_interaction_reference,
      mention_location: location_reference,
      include_emoji: emoji_suggestions,
      hashtags: relevant_hashtags
    }
  end
  
  def generate_follow_up_plan
    {
      immediate_action: determine_immediate_action,
      follow_up_timing: calculate_follow_up_timing,
      escalation_needed: needs_escalation?,
      internal_notes: generate_internal_notes,
      next_steps: suggest_next_steps
    }
  end
  
  def calculate_historical_success(tone)
    # Query historical response performance by tone
    successful = Lead.joins(:mention)
                    .where(mentions: { response_tone: tone })
                    .where(status: ['qualified', 'converted'])
                    .count
    
    total = Lead.joins(:mention)
                .where(mentions: { response_tone: tone })
                .count
    
    return 0.5 if total.zero?
    (successful.to_f / total).clamp(0, 1)
  end
  
  def calculate_context_match(tone)
    matches = {
      'professional' => %w[business inquiry technical],
      'friendly' => %w[casual social positive],
      'empathetic' => %w[complaint issue problem negative],
      'enthusiastic' => %w[praise excited positive purchase],
      'apologetic' => %w[complaint angry frustrated negative],
      'helpful' => %w[question help support need]
    }
    
    relevant_keywords = matches[tone] || []
    content_lower = @mention.content.downcase
    
    match_score = relevant_keywords.count { |keyword| content_lower.include?(keyword) }
    (match_score.to_f / relevant_keywords.length).clamp(0, 1)
  end
  
  def calculate_platform_fit(tone)
    platform_preferences = {
      'twitter' => { 'friendly' => 0.8, 'professional' => 0.6, 'enthusiastic' => 0.7 },
      'linkedin' => { 'professional' => 0.9, 'friendly' => 0.5, 'helpful' => 0.8 },
      'facebook' => { 'friendly' => 0.9, 'empathetic' => 0.8, 'helpful' => 0.7 },
      'reddit' => { 'helpful' => 0.9, 'professional' => 0.5, 'friendly' => 0.6 }
    }
    
    platform_preferences.dig(@mention.platform, tone) || 0.5
  end
end
```

### 4. Elasticsearch Integration
```ruby
# app/models/concerns/searchable.rb
module Searchable
  extend ActiveSupport::Concern
  
  included do
    include Elasticsearch::Model
    include Elasticsearch::Model::Callbacks
    
    after_commit :index_document, on: [:create, :update]
    after_commit :delete_document, on: :destroy
    
    settings index: {
      number_of_shards: 1,
      number_of_replicas: 0,
      analysis: {
        analyzer: {
          custom_analyzer: {
            type: 'custom',
            tokenizer: 'standard',
            filter: ['lowercase', 'stop', 'snowball', 'synonym']
          }
        },
        filter: {
          synonym: {
            type: 'synonym',
            synonyms_path: 'synonyms.txt'
          }
        }
      }
    }
  end
  
  module ClassMethods
    def search_with_aggregations(query, options = {})
      search_definition = {
        query: build_query(query, options),
        aggs: build_aggregations(options),
        highlight: highlight_fields,
        size: options[:per_page] || 20,
        from: ((options[:page] || 1) - 1) * (options[:per_page] || 20)
      }
      
      __elasticsearch__.search(search_definition)
    end
    
    private
    
    def build_query(query, options)
      if query.present?
        {
          bool: {
            must: [
              {
                multi_match: {
                  query: query,
                  fields: searchable_fields,
                  type: 'best_fields',
                  fuzziness: 'AUTO'
                }
              }
            ],
            filter: build_filters(options)
          }
        }
      else
        {
          bool: {
            must: { match_all: {} },
            filter: build_filters(options)
          }
        }
      end
    end
    
    def build_filters(options)
      filters = []
      
      if options[:status].present?
        filters << { term: { status: options[:status] } }
      end
      
      if options[:date_range].present?
        filters << {
          range: {
            created_at: {
              gte: options[:date_range][:from],
              lte: options[:date_range][:to]
            }
          }
        }
      end
      
      if options[:score_range].present?
        filters << {
          range: {
            score: {
              gte: options[:score_range][:min],
              lte: options[:score_range][:max]
            }
          }
        }
      end
      
      filters
    end
    
    def build_aggregations(options)
      {
        status_counts: {
          terms: { field: 'status.keyword' }
        },
        score_distribution: {
          histogram: {
            field: 'score',
            interval: 10
          }
        },
        daily_counts: {
          date_histogram: {
            field: 'created_at',
            calendar_interval: 'day'
          }
        },
        top_keywords: {
          terms: {
            field: 'keyword.keyword',
            size: 10
          }
        }
      }
    end
    
    def searchable_fields
      ['content^3', 'author^2', 'notes', 'tags']
    end
    
    def highlight_fields
      {
        fields: {
          content: {},
          author: {},
          notes: {}
        }
      }
    end
  end
end

# app/models/mention.rb
class Mention < ApplicationRecord
  include Searchable
  
  def as_indexed_json(options = {})
    {
      id: id,
      content: content,
      author: author,
      platform: platform,
      keyword: keyword.keyword,
      sentiment_score: analysis_result&.sentiment_score,
      lead_score: analysis_result&.lead_score,
      topics: analysis_result&.topics,
      entities: analysis_result&.entities,
      created_at: created_at,
      has_lead: lead.present?,
      lead_status: lead&.status
    }
  end
end

# app/controllers/search_controller.rb
class SearchController < ApplicationController
  def index
    @results = perform_search
    @aggregations = extract_aggregations(@results)
    
    respond_to do |format|
      format.html
      format.json { render json: { results: @results, aggregations: @aggregations } }
    end
  end
  
  private
  
  def perform_search
    Mention.search_with_aggregations(
      params[:q],
      status: params[:status],
      date_range: date_range_params,
      score_range: score_range_params,
      page: params[:page],
      per_page: params[:per_page]
    )
  end
  
  def extract_aggregations(results)
    {
      status_counts: results.aggregations.status_counts.buckets,
      score_distribution: results.aggregations.score_distribution.buckets,
      daily_counts: results.aggregations.daily_counts.buckets,
      top_keywords: results.aggregations.top_keywords.buckets
    }
  end
  
  def date_range_params
    return unless params[:date_from] || params[:date_to]
    
    {
      from: params[:date_from] || 1.year.ago,
      to: params[:date_to] || Time.current
    }
  end
  
  def score_range_params
    return unless params[:score_min] || params[:score_max]
    
    {
      min: params[:score_min] || 0,
      max: params[:score_max] || 100
    }
  end
end
```

### 5. Recommendation Engine
```ruby
# app/services/recommendation_service.rb
class RecommendationService
  def initialize(user)
    @user = user
  end
  
  def generate_recommendations
    {
      keywords: recommend_keywords,
      leads: recommend_similar_leads,
      strategies: recommend_strategies,
      integrations: recommend_integrations,
      content: recommend_content,
      actions: recommend_next_actions
    }
  end
  
  private
  
  def recommend_keywords
    # Collaborative filtering + content-based recommendations
    similar_users = find_similar_users
    popular_keywords = Keyword.joins(:user)
                              .where(users: { id: similar_users.pluck(:id) })
                              .group(:keyword)
                              .order('COUNT(*) DESC')
                              .limit(10)
                              .pluck(:keyword)
    
    # Content-based recommendations from existing keywords
    related_keywords = @user.keywords.flat_map do |keyword|
      find_related_terms(keyword.keyword)
    end.uniq
    
    # Trending keywords in industry
    trending = detect_trending_keywords
    
    {
      collaborative: popular_keywords,
      content_based: related_keywords,
      trending: trending
    }.values.flatten.uniq.first(20)
  end
  
  def recommend_similar_leads
    return [] if @user.leads.empty?
    
    # Get characteristics of converted leads
    converted_leads = @user.leads.converted
    
    # Find similar unconverted leads
    Lead.joins(:mention)
        .where.not(id: @user.leads.pluck(:id))
        .where(
          mentions: {
            sentiment_score: converted_leads.average(:sentiment_score) - 0.2..converted_leads.average(:sentiment_score) + 0.2
          }
        )
        .order(score: :desc)
        .limit(10)
  end
  
  def recommend_strategies
    performance_data = analyze_user_performance
    
    strategies = []
    
    # Response time optimization
    if performance_data[:avg_response_time] > 2.hours
      strategies << {
        type: 'response_time',
        title: 'Improve Response Time',
        description: 'Your average response time is #{performance_data[:avg_response_time].in_hours} hours. Faster responses increase conversion by 40%.',
        action: 'Enable real-time notifications'
      }
    end
    
    # Keyword optimization
    underperforming = @user.keywords.where('conversion_rate < ?', 5)
    if underperforming.any?
      strategies << {
        type: 'keyword_optimization',
        title: 'Optimize Underperforming Keywords',
        keywords: underperforming.pluck(:keyword),
        description: 'These keywords have low conversion rates. Consider refining or replacing them.',
        action: 'Review keyword strategy'
      }
    end
    
    # Platform expansion
    unused_platforms = %w[twitter linkedin reddit facebook] - @user.integrations.pluck(:platform)
    if unused_platforms.any?
      strategies << {
        type: 'platform_expansion',
        title: 'Expand to New Platforms',
        platforms: unused_platforms,
        description: 'Increase reach by adding more social platforms.',
        action: 'Connect new platforms'
      }
    end
    
    strategies
  end
  
  def recommend_integrations
    current_integrations = @user.integrations.pluck(:provider)
    
    recommendations = []
    
    # CRM recommendation
    if !current_integrations.include?('salesforce') && @user.leads.count > 50
      recommendations << {
        integration: 'salesforce',
        reason: 'Manage growing lead pipeline more effectively',
        benefit: 'Automate lead sync and improve sales collaboration'
      }
    end
    
    # Email marketing recommendation
    if !current_integrations.include?('mailchimp') && @user.leads.qualified.count > 20
      recommendations << {
        integration: 'mailchimp',
        reason: 'Nurture qualified leads with email campaigns',
        benefit: 'Increase conversion rates by 25% with email nurturing'
      }
    end
    
    # Slack recommendation
    if !current_integrations.include?('slack') && @user.organization&.users&.count > 3
      recommendations << {
        integration: 'slack',
        reason: 'Improve team collaboration on leads',
        benefit: 'Reduce response time by 50% with instant notifications'
      }
    end
    
    recommendations
  end
  
  def recommend_content
    # Analyze successful mentions and generate content ideas
    successful_mentions = Mention.joins(:lead)
                                 .where(leads: { status: ['qualified', 'converted'] })
                                 .where(keyword_id: @user.keywords.pluck(:id))
    
    topics = extract_successful_topics(successful_mentions)
    formats = analyze_successful_formats(successful_mentions)
    
    {
      topics: topics,
      formats: formats,
      templates: generate_content_templates(topics, formats)
    }
  end
  
  def recommend_next_actions
    actions = []
    
    # Uncontacted qualified leads
    uncontacted = @user.leads.qualified.where(last_contacted_at: nil)
    if uncontacted.any?
      actions << {
        priority: 'high',
        action: 'Contact qualified leads',
        count: uncontacted.count,
        estimated_value: uncontacted.sum(:estimated_value)
      }
    end
    
    # Follow-up needed
    need_followup = @user.leads
                         .where('last_contacted_at < ?', 3.days.ago)
                         .where.not(status: ['converted', 'lost'])
    if need_followup.any?
      actions << {
        priority: 'medium',
        action: 'Follow up with leads',
        count: need_followup.count,
        leads: need_followup.limit(5)
      }
    end
    
    # Keywords needing review
    inactive_keywords = @user.keywords
                            .where('mentions_count = 0')
                            .where('created_at < ?', 7.days.ago)
    if inactive_keywords.any?
      actions << {
        priority: 'low',
        action: 'Review inactive keywords',
        keywords: inactive_keywords.pluck(:keyword)
      }
    end
    
    actions.sort_by { |a| priority_value(a[:priority]) }
  end
  
  def find_similar_users
    # Find users with similar keyword patterns
    User.joins(:keywords)
        .where(keywords: { keyword: @user.keywords.pluck(:keyword) })
        .where.not(id: @user.id)
        .group(:id)
        .order('COUNT(*) DESC')
        .limit(10)
  end
  
  def find_related_terms(keyword)
    # Would integrate with external API or ML model
    # For now, simple related terms
    []
  end
  
  def detect_trending_keywords
    # Keywords with increasing mention velocity
    Keyword.joins(:mentions)
           .where('mentions.created_at > ?', 7.days.ago)
           .group(:keyword)
           .order('COUNT(*) DESC')
           .limit(10)
           .pluck(:keyword)
  end
  
  def analyze_user_performance
    {
      avg_response_time: calculate_avg_response_time,
      conversion_rate: @user.conversion_rate,
      lead_quality_score: calculate_avg_lead_score,
      keyword_effectiveness: analyze_keyword_effectiveness
    }
  end
  
  def priority_value(priority)
    { 'high' => 0, 'medium' => 1, 'low' => 2 }.fetch(priority, 3)
  end
end
```

## 🧪 Testing Strategy

### AI Analysis Testing
```ruby
# test/services/ai_analysis_service_test.rb
class AiAnalysisServiceTest < ActiveSupport::TestCase
  setup do
    @mention = mentions(:positive_mention)
    @service = AiAnalysisService.new(@mention)
  end
  
  test "should analyze sentiment correctly" do
    VCR.use_cassette('openai_sentiment_analysis') do
      result = @service.comprehensive_analysis
      
      assert result[:sentiment].present?
      assert_includes %w[positive negative neutral], result[:sentiment][:overall]
      assert result[:sentiment][:confidence].between?(0, 1)
    end
  end
  
  test "should extract entities" do
    VCR.use_cassette('openai_entity_extraction') do
      result = @service.comprehensive_analysis
      
      assert result[:entities].present?
      assert result[:entities][:people].is_a?(Array)
      assert result[:entities][:organizations].is_a?(Array)
    end
  end
  
  test "should calculate lead score" do
    result = @service.comprehensive_analysis
    
    assert result[:lead_score].present?
    assert result[:lead_score].between?(0, 100)
  end
end
```

### ML Model Testing
```ruby
# test/services/lead_scoring_service_test.rb
class LeadScoringServiceTest < ActiveSupport::TestCase
  test "should calculate score for high-value mention" do
    mention = mentions(:high_value_mention)
    mention.update(
      author_followers: 10000,
      likes_count: 100,
      sentiment_score: 0.8
    )
    
    score = LeadScoringService.new(mention).calculate
    
    assert score > 70, "High-value mention should score above 70"
  end
  
  test "should calculate score for low-value mention" do
    mention = mentions(:low_value_mention)
    mention.update(
      author_followers: 10,
      likes_count: 0,
      sentiment_score: -0.5
    )
    
    score = LeadScoringService.new(mention).calculate
    
    assert score < 30, "Low-value mention should score below 30"
  end
  
  test "should handle missing features gracefully" do
    mention = mentions(:incomplete_mention)
    
    assert_nothing_raised do
      score = LeadScoringService.new(mention).calculate
      assert score.between?(0, 100)
    end
  end
end
```

## 🚀 Deployment Plan

### Pre-deployment
- [ ] AI model training completed
- [ ] Elasticsearch cluster configured
- [ ] ML models validated
- [ ] API rate limits configured
- [ ] Cost projections verified

### Deployment Steps
1. Deploy Elasticsearch infrastructure
2. Index existing data
3. Deploy AI analysis services
4. Enable ML scoring
5. Activate recommendation engine
6. Monitor performance and costs

### Post-deployment
- [ ] Monitor AI API usage and costs
- [ ] Track model performance metrics
- [ ] Gather user feedback on recommendations
- [ ] A/B test response suggestions
- [ ] Continuously train ML models

---

**Status**: Ready for Implementation  
**Timeline**: 3-4 Weeks  
**Priority**: Medium  
**Business Impact**: Competitive differentiation through advanced AI capabilities