# lib/tasks/counters.rake
namespace :counters do
  desc "Reset all counter caches for keywords and users"
  task reset: :environment do
    puts "Starting counter cache reset..."

    # Reset keyword counters
    print "Resetting keyword counters..."
    keyword_count = 0
    Keyword.find_each do |keyword|
      Keyword.reset_counters(keyword.id, :mentions)
      Keyword.reset_counters(keyword.id, :leads)
      keyword_count += 1
      print "." if keyword_count % 100 == 0
    end
    puts " Done! (#{keyword_count} keywords)"

    # Reset user counters
    print "Resetting user counters..."
    user_count = 0
    User.find_each do |user|
      User.reset_counters(user.id, :keywords)
      User.reset_counters(user.id, :leads)
      User.reset_counters(user.id, :mentions)
      user_count += 1
      print "." if user_count % 100 == 0
    end
    puts " Done! (#{user_count} users)"

    puts "Counter cache reset complete!"
  end

  desc "Reset counters for a specific user"
  task :reset_for_user, [:user_id] => :environment do |_t, args|
    user = User.find(args[:user_id])

    # Reset user's own counters
    User.reset_counters(user.id, :keywords, :leads, :mentions)

    # Reset counters for user's keywords
    user.keywords.find_each do |keyword|
      Keyword.reset_counters(keyword.id, :mentions, :leads)
    end

    puts "Counter caches reset for user #{user.id}"
  end
end