<div class="min-h-screen bg-gray-50 py-8">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
      <div class="flex justify-between items-center">
        <div class="flex items-center">
          <%= link_to integration_path(@integration), class: "mr-4 text-gray-400 hover:text-gray-500" do %>
            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
          <% end %>
          <div>
            <h1 class="text-3xl font-bold text-gray-900">
              Activity Logs - <%= @integration.platform_name&.capitalize || @integration.provider %>
            </h1>
            <p class="mt-1 text-sm text-gray-500">
              Detailed activity history for this integration
            </p>
          </div>
        </div>
        <div class="flex space-x-3">
          <%= link_to "Clear Logs", "#", 
              data: { confirm: "Are you sure? This will permanently delete all logs for this integration." },
              class: "inline-flex items-center px-4 py-2 border border-red-300 shadow-sm text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50" %>
          <%= link_to "Export CSV", "#", 
              class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" %>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="mb-6 bg-white shadow overflow-hidden sm:rounded-lg">
      <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
          Filter Logs
        </h3>
      </div>
      <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-4">
          <div>
            <label class="block text-sm font-medium text-gray-700">Activity Type</label>
            <select class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
              <option value="">All Types</option>
              <option value="connection">Connection</option>
              <option value="sync">Sync</option>
              <option value="error">Error</option>
              <option value="authentication">Authentication</option>
              <option value="configuration">Configuration</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Status</label>
            <select class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
              <option value="">All Statuses</option>
              <option value="success">Success</option>
              <option value="error">Error</option>
              <option value="warning">Warning</option>
              <option value="info">Info</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Date Range</label>
            <select class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
              <option value="today">Today</option>
              <option value="week">Last 7 days</option>
              <option value="month">Last 30 days</option>
              <option value="all">All time</option>
            </select>
          </div>
          <div class="flex items-end">
            <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              Apply Filters
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Logs Table -->
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
      <% if @logs&.any? %>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Timestamp
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Activity Type
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Details
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Metadata
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% @logs.each do |log| %>
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <%= log.performed_at.strftime("%b %d, %Y %H:%M:%S") %>
                    <div class="text-xs text-gray-500">
                      <%= time_ago_in_words(log.performed_at) %> ago
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                      <%= log.activity_type.humanize %>
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <% if log.success? %>
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                        Success
                      </span>
                    <% elsif log.error? %>
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                        Error
                      </span>
                    <% elsif log.status == 'warning' %>
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                        Warning
                      </span>
                    <% else %>
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                        <%= log.status&.humanize || 'Info' %>
                      </span>
                    <% end %>
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-900">
                    <div class="max-w-xs overflow-hidden">
                      <%= truncate(log.details, length: 150) %>
                      <% if log.details&.length.to_i > 150 %>
                        <button type="button" class="text-indigo-600 hover:text-indigo-500 text-xs">
                          Show more
                        </button>
                      <% end %>
                    </div>
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    <% if log.details.present? %>
                      <details class="cursor-pointer">
                        <summary class="text-xs text-indigo-600 hover:text-indigo-500">
                          View metadata
                        </summary>
                        <pre class="mt-2 text-xs bg-gray-50 p-2 rounded overflow-x-auto max-w-xs">
<%= log.details %>
                        </pre>
                      </details>
                    <% else %>
                      <span class="text-xs text-gray-400">No metadata</span>
                    <% end %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
        
        <!-- Pagination -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Previous
            </a>
            <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Next
            </a>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Showing
                <span class="font-medium">1</span>
                to
                <span class="font-medium"><%= [@logs.count, 50].min %></span>
                of
                <span class="font-medium"><%= @logs.count %></span>
                results
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <!-- Pagination controls would go here -->
              </nav>
            </div>
          </div>
        </div>
      <% else %>
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No activity logs</h3>
          <p class="mt-1 text-sm text-gray-500">No activity has been recorded for this integration yet.</p>
        </div>
      <% end %>
    </div>
  </div>
</div>