<div class="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50">
  <!-- Integrations Analytics Header -->
  <div class="bg-white/80 backdrop-blur-sm border-b border-indigo-100 sticky top-0 z-40">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
              Integration Analytics
            </h1>
            <p class="mt-1 text-gray-600">Integration performance and ROI analysis</p>
          </div>
        </div>
        
        <!-- Navigation Tabs -->
        <div class="mt-4 flex space-x-1 border-b border-gray-200">
          <%= link_to "Overview", analytics_path, class: "px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-t-lg" %>
          <%= link_to "Performance", analytics_performance_path, class: "px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-t-lg" %>
          <%= link_to "Trends", analytics_trends_path, class: "px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-t-lg" %>
          <%= link_to "Keywords", analytics_keywords_path, class: "px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-t-lg" %>
          <%= link_to "Leads", analytics_leads_path, class: "px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-t-lg" %>
          <%= link_to "Integrations", analytics_integrations_path, class: "px-4 py-2 text-sm font-medium text-indigo-600 border-b-2 border-indigo-600 bg-indigo-50 rounded-t-lg" %>
        </div>
      </div>
    </div>
  </div>

  <!-- Integrations Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Integration Performance Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <% (@integration_metrics || []).each do |integration| %>
        <div class="bg-white rounded-2xl shadow-sm p-6 border border-gray-100">
          <div class="flex items-center justify-between mb-4">
            <h4 class="text-lg font-semibold text-gray-900"><%= integration[:name] %></h4>
            <span class="px-2 py-1 text-xs font-medium text-green-600 bg-green-100 rounded-full">Active</span>
          </div>
          
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">Mentions</span>
              <span class="text-sm font-medium text-gray-900"><%= integration[:mentions_count] %></span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">Leads</span>
              <span class="text-sm font-medium text-gray-900"><%= integration[:leads_count] %></span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">Conversion Rate</span>
              <span class="text-sm font-medium text-indigo-600"><%= integration[:conversion_rate] %>%</span>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>