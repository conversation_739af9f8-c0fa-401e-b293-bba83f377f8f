<div class="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50">
  <!-- Leads Analytics Header -->
  <div class="bg-white/80 backdrop-blur-sm border-b border-indigo-100 sticky top-0 z-40">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
              Lead Analytics
            </h1>
            <p class="mt-1 text-gray-600">Lead quality analysis and cohort performance</p>
          </div>
        </div>
        
        <!-- Navigation Tabs -->
        <div class="mt-4 flex space-x-1 border-b border-gray-200">
          <%= link_to "Overview", analytics_path, class: "px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-t-lg" %>
          <%= link_to "Performance", analytics_performance_path, class: "px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-t-lg" %>
          <%= link_to "Trends", analytics_trends_path, class: "px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-t-lg" %>
          <%= link_to "Keywords", analytics_keywords_path, class: "px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-t-lg" %>
          <%= link_to "Leads", analytics_leads_path, class: "px-4 py-2 text-sm font-medium text-indigo-600 border-b-2 border-indigo-600 bg-indigo-50 rounded-t-lg" %>
          <%= link_to "Integrations", analytics_integrations_path, class: "px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-t-lg" %>
        </div>
      </div>
    </div>
  </div>

  <!-- Leads Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Cohort Analysis -->
    <div class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden mb-8">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Cohort Analysis</h3>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cohort</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Leads</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contacted</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Converted</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Score</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% (@cohort_analysis || {}).each do |cohort_name, data| %>
              <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  <%= cohort_name %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <%= data[:total] %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <%= data[:contacted] %> 
                  <span class="text-gray-500">(<%= data[:total] > 0 ? number_to_percentage(data[:contacted].to_f / data[:total] * 100, precision: 0) : '0%' %>)</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <%= data[:converted] %>
                  <span class="text-gray-500">(<%= data[:total] > 0 ? number_to_percentage(data[:converted].to_f / data[:total] * 100, precision: 0) : '0%' %>)</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <%= data[:avg_score] %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>