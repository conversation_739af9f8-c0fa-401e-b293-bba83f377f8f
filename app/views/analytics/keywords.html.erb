<div class="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50">
  <!-- Keywords Analytics Header -->
  <div class="bg-white/80 backdrop-blur-sm border-b border-indigo-100 sticky top-0 z-40">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
              Keyword Analytics
            </h1>
            <p class="mt-1 text-gray-600">Keyword performance analysis and optimization insights</p>
          </div>
        </div>
        
        <!-- Navigation Tabs -->
        <div class="mt-4 flex space-x-1 border-b border-gray-200">
          <%= link_to "Overview", analytics_path, class: "px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-t-lg" %>
          <%= link_to "Performance", analytics_performance_path, class: "px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-t-lg" %>
          <%= link_to "Trends", analytics_trends_path, class: "px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-t-lg" %>
          <%= link_to "Keywords", analytics_keywords_path, class: "px-4 py-2 text-sm font-medium text-indigo-600 border-b-2 border-indigo-600 bg-indigo-50 rounded-t-lg" %>
          <%= link_to "Leads", analytics_leads_path, class: "px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-t-lg" %>
          <%= link_to "Integrations", analytics_integrations_path, class: "px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-t-lg" %>
        </div>
      </div>
    </div>
  </div>

  <!-- Keywords Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Keyword Performance Table -->
    <div class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Keyword Performance Analysis</h3>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Keyword</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mentions</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Leads</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Conversion Rate</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Score</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trend</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% (@keyword_performance || []).each do |keyword| %>
              <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  <%= keyword[:keyword] %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <%= number_with_delimiter(keyword[:mentions_count]) %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <%= number_with_delimiter(keyword[:leads_count]) %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm">
                  <span class="<%= keyword[:conversion_rate] > 10 ? 'text-green-600' : 'text-gray-900' %> font-medium">
                    <%= number_to_percentage(keyword[:conversion_rate], precision: 1) %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <%= number_with_precision(keyword[:avg_lead_score], precision: 1) %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm">
                  <% trend = keyword[:trend] || 'stable' %>
                  <span class="<%= trend == 'up' ? 'text-green-600' : trend == 'down' ? 'text-red-600' : 'text-gray-600' %>">
                    <%= trend == 'up' ? '↑' : trend == 'down' ? '↓' : '→' %>
                  </span>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>