<% content_for :title, "About Us - AI Lead Gen" %>

<!-- Hero Section -->
<section class="relative bg-gradient-to-br from-indigo-600 to-purple-700 text-white py-20">
  <div class="absolute inset-0 bg-black opacity-20"></div>
  <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
    <div class="max-w-4xl mx-auto text-center" data-controller="fade-in">
      <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6" data-fade-in-target="element">
        Transforming Lead Generation with AI
      </h1>
      <p class="text-xl md:text-2xl text-indigo-100 mb-8" data-fade-in-target="element" data-fade-in-delay="100">
        We're on a mission to help businesses discover and connect with their ideal customers through intelligent automation.
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center" data-fade-in-target="element" data-fade-in-delay="200">
        <%= link_to "Get Started", new_user_registration_path, class: "inline-block bg-white text-indigo-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-300" %>
        <%= link_to "Contact Us", contact_path, class: "inline-block border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-indigo-600 transition duration-300" %>
      </div>
    </div>
  </div>
</section>

<!-- Company Overview -->
<section class="py-16 bg-white">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="max-w-6xl mx-auto">
      <div class="grid md:grid-cols-2 gap-12 items-center">
        <div data-controller="scroll-reveal" data-scroll-reveal-target="element">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">Our Story</h2>
          <p class="text-gray-600 mb-4 leading-relaxed">
            Founded in 2021, AI Lead Gen emerged from a simple observation: businesses were spending countless hours manually searching for leads across various platforms, often missing valuable opportunities.
          </p>
          <p class="text-gray-600 mb-4 leading-relaxed">
            Our founders, with backgrounds in artificial intelligence and B2B sales, saw an opportunity to revolutionize this process. By combining cutting-edge AI technology with deep understanding of sales processes, we created a platform that not only finds leads but qualifies them intelligently.
          </p>
          <p class="text-gray-600 leading-relaxed">
            Today, we serve thousands of businesses worldwide, helping them save time, reduce costs, and dramatically improve their lead quality. Our AI analyzes millions of data points daily, identifying opportunities that human researchers might miss.
          </p>
        </div>
        <div class="relative" data-controller="scroll-reveal" data-scroll-reveal-target="element" data-scroll-reveal-delay="200">
          <img src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=800&h=600&fit=crop" 
               alt="Team collaboration" 
               class="rounded-lg shadow-xl">
          <div class="absolute -bottom-6 -right-6 bg-indigo-600 text-white p-6 rounded-lg shadow-lg">
            <div class="text-3xl font-bold">5000+</div>
            <div class="text-sm">Happy Customers</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Company Values -->
<section class="py-16 bg-gray-50">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="max-w-6xl mx-auto">
      <div class="text-center mb-12" data-controller="scroll-reveal" data-scroll-reveal-target="element">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Values</h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          These core values guide everything we do, from product development to customer support.
        </p>
      </div>
      
      <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
        <% @company_values.each_with_index do |value, index| %>
          <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300" 
               data-controller="scroll-reveal" 
               data-scroll-reveal-target="element" 
               data-scroll-reveal-delay="<%= index * 100 %>">
            <div class="w-14 h-14 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
              <% if value[:icon] == "lightbulb" %>
                <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
              <% elsif value[:icon] == "users" %>
                <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                </svg>
              <% elsif value[:icon] == "shield" %>
                <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
              <% else %>
                <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              <% end %>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2"><%= value[:title] %></h3>
            <p class="text-gray-600"><%= value[:description] %></p>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</section>

<!-- Team Section -->
<section class="py-16 bg-white">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="max-w-6xl mx-auto">
      <div class="text-center mb-12" data-controller="scroll-reveal" data-scroll-reveal-target="element">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Meet Our Team</h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Passionate experts dedicated to revolutionizing how businesses find and connect with customers.
        </p>
      </div>
      
      <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
        <% @team_members.each_with_index do |member, index| %>
          <div class="text-center" data-controller="scroll-reveal" data-scroll-reveal-target="element" data-scroll-reveal-delay="<%= index * 100 %>">
            <div class="relative mb-4 group">
              <img src="<%= member[:image] %>" 
                   alt="<%= member[:name] %>" 
                   class="w-32 h-32 mx-auto rounded-full object-cover shadow-lg group-hover:shadow-xl transition-shadow duration-300">
              <div class="absolute inset-0 w-32 h-32 mx-auto rounded-full bg-indigo-600 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-1"><%= member[:name] %></h3>
            <p class="text-indigo-600 font-medium mb-2"><%= member[:role] %></p>
            <p class="text-gray-600 text-sm"><%= member[:bio] %></p>
            <div class="flex justify-center space-x-3 mt-4">
              <a href="#" class="text-gray-400 hover:text-indigo-600 transition-colors duration-300">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"></path>
                </svg>
              </a>
              <a href="#" class="text-gray-400 hover:text-indigo-600 transition-colors duration-300">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                </svg>
              </a>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</section>

<!-- Timeline Section -->
<section class="py-16 bg-gray-50">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
      <div class="text-center mb-12" data-controller="scroll-reveal" data-scroll-reveal-target="element">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Journey</h2>
        <p class="text-xl text-gray-600">
          From startup to industry leader in just a few years.
        </p>
      </div>
      
      <div class="relative">
        <!-- Timeline line -->
        <div class="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-indigo-200"></div>
        
        <% @milestones.each_with_index do |milestone, index| %>
          <div class="relative flex items-center mb-8 <%= index.even? ? 'justify-start' : 'justify-end' %>" 
               data-controller="scroll-reveal" 
               data-scroll-reveal-target="element" 
               data-scroll-reveal-delay="<%= index * 150 %>">
            <div class="w-5/12 <%= index.even? ? 'text-right pr-8' : 'text-left pl-8' %>">
              <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
                <span class="text-indigo-600 font-bold text-lg"><%= milestone[:year] %></span>
                <h3 class="text-xl font-semibold text-gray-900 mt-2 mb-2"><%= milestone[:title] %></h3>
                <p class="text-gray-600"><%= milestone[:description] %></p>
              </div>
            </div>
            <div class="absolute left-1/2 transform -translate-x-1/2 w-8 h-8 bg-indigo-600 rounded-full border-4 border-white shadow-md flex items-center justify-center">
              <div class="w-2 h-2 bg-white rounded-full"></div>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="py-16 bg-gradient-to-r from-indigo-600 to-purple-700 text-white">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto text-center" data-controller="scroll-reveal" data-scroll-reveal-target="element">
      <h2 class="text-3xl md:text-4xl font-bold mb-4">Ready to Transform Your Lead Generation?</h2>
      <p class="text-xl mb-8 text-indigo-100">
        Join thousands of businesses that are already using AI Lead Gen to supercharge their sales pipeline.
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <%= link_to "Start Free Trial", new_user_registration_path, class: "inline-block bg-white text-indigo-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-300" %>
        <%= link_to "Schedule a Demo", contact_path, class: "inline-block border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-indigo-600 transition duration-300" %>
      </div>
    </div>
  </div>
</section>