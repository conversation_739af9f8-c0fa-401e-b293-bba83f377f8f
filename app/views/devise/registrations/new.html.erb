<% content_for :title, "Sign Up" %>

<div class="w-full">
  <h2 class="text-3xl font-bold text-gray-900 mb-2">Create your account</h2>
  <p class="text-gray-600 mb-8">Start your free trial and discover powerful AI-driven lead generation</p>

  <%= form_for(resource, as: resource_name, url: registration_path(resource_name), html: { class: "space-y-5", data: { turbo: false, controller: "form-validation" } }) do |f| %>
    <!-- Error Messages -->
    <% if resource.errors.any? %>
      <div class="p-4 bg-red-50 border border-red-200 rounded-lg" role="alert">
        <div class="flex">
          <svg class="w-5 h-5 text-red-600 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
          </svg>
          <div class="text-sm text-red-800">
            <p class="font-medium mb-1">Please fix the following errors:</p>
            <ul class="list-disc list-inside space-y-1">
              <% resource.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Email Field -->
    <div>
      <%= f.label :email, class: "block text-sm font-medium text-gray-700 mb-1" %>
      <div class="relative">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
          </svg>
        </div>
        <%= f.email_field :email, 
            autofocus: true, 
            autocomplete: "email",
            required: true,
            placeholder: "<EMAIL>",
            class: "block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",
            "aria-label": "Email address",
            "aria-required": "true",
            "aria-describedby": "email-description",
            data: { 
              form_validation_target: "email",
              action: "blur->form-validation#validateEmail"
            } %>
      </div>
      <p id="email-description" class="mt-1 text-xs text-gray-500">We'll use this for your account and notifications</p>
    </div>

    <!-- Password Field -->
    <div data-controller="password-strength" data-action="password-strength:strengthChanged->form-validation#passwordStrengthChanged">
      <%= f.label :password, class: "block text-sm font-medium text-gray-700 mb-1" %>
      <div class="relative" data-controller="password-toggle">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
          </svg>
        </div>
        <%= f.password_field :password, 
            autocomplete: "new-password",
            required: true,
            placeholder: "Create a strong password",
            class: "block w-full pl-10 pr-10 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",
            "aria-label": "Password",
            "aria-required": "true",
            "aria-describedby": "password-strength",
            data: { 
              password_toggle_target: "input",
              password_strength_target: "password",
              form_validation_target: "password",
              action: "blur->form-validation#validatePassword"
            } %>
        <button type="button" 
                class="absolute inset-y-0 right-0 pr-3 flex items-center"
                data-action="click->password-toggle#toggle"
                data-password-toggle-target="button">
          <svg class="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-password-toggle-target="showIcon">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          </svg>
          <svg class="h-5 w-5 text-gray-400 hover:text-gray-600 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-password-toggle-target="hideIcon">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"></path>
          </svg>
        </button>
      </div>
      
      <!-- Password Strength Indicator -->
      <div class="mt-2" id="password-strength">
        <div class="flex items-center justify-between mb-1">
          <span class="text-xs text-gray-500">Password strength</span>
          <span class="text-xs text-gray-500" data-password-strength-target="label">Enter a password</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-1.5">
          <div class="h-1.5 rounded-full transition-all duration-300 bg-gray-300" 
               data-password-strength-target="bar"
               style="width: 0%"></div>
        </div>
        <p class="mt-1 text-xs text-gray-500">
          <% if @minimum_password_length %>
            Minimum <%= @minimum_password_length %> characters
          <% end %>
          • Include numbers and symbols for better security
        </p>
        <!-- Password Suggestions -->
        <ul class="mt-2 space-y-1 text-gray-600 hidden" data-password-strength-target="suggestions">
          <li class="flex items-start">
            <svg class="w-4 h-4 text-gray-400 mr-1 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-xs">Start typing to see suggestions</span>
          </li>
        </ul>
      </div>
    </div>

    <!-- Password Confirmation Field -->
    <div>
      <%= f.label :password_confirmation, "Confirm password", class: "block text-sm font-medium text-gray-700 mb-1" %>
      <div class="relative">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <%= f.password_field :password_confirmation, 
            autocomplete: "new-password",
            required: true,
            placeholder: "Confirm your password",
            class: "block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",
            "aria-label": "Confirm password",
            "aria-required": "true",
            data: { 
              form_validation_target: "passwordConfirmation",
              action: "blur->form-validation#validatePasswordConfirmation"
            } %>
      </div>
    </div>

    <!-- Terms and Privacy -->
    <div class="space-y-3">
      <div class="flex items-start">
        <input type="checkbox" 
               id="terms" 
               required
               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-0.5">
        <label for="terms" class="ml-2 block text-sm text-gray-700">
          I agree to the 
          <%= link_to "Terms of Service", "#", class: "text-blue-600 hover:text-blue-700 underline" %>
          and 
          <%= link_to "Privacy Policy", "#", class: "text-blue-600 hover:text-blue-700 underline" %>
        </label>
      </div>
      
      <div class="flex items-start">
        <input type="checkbox" 
               id="marketing" 
               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-0.5">
        <label for="marketing" class="ml-2 block text-sm text-gray-700">
          Send me product updates and marketing emails
        </label>
      </div>
    </div>

    <!-- Submit Button -->
    <div>
      <%= f.submit "Create account", 
          class: "w-full flex justify-center py-2.5 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:scale-[1.02]",
          data: { 
            disable_with: "Creating account...",
            form_validation_target: "submit"
          } %>
    </div>
  <% end %>

  <!-- Divider -->
  <div class="relative my-6">
    <div class="absolute inset-0 flex items-center">
      <div class="w-full border-t border-gray-300"></div>
    </div>
    <div class="relative flex justify-center text-sm">
      <span class="px-4 bg-white text-gray-500">Or sign up with</span>
    </div>
  </div>

  <!-- Social Signup Buttons -->
  <div class="grid grid-cols-3 gap-3">
    <button type="button" class="w-full inline-flex justify-center py-2.5 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-400 transition-all group">
      <svg class="w-5 h-5 group-hover:text-gray-700" viewBox="0 0 24 24" fill="currentColor">
        <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
        <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
        <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
        <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
      </svg>
      <span class="sr-only">Sign up with Google</span>
    </button>

    <button type="button" class="w-full inline-flex justify-center py-2.5 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-400 transition-all group">
      <svg class="w-5 h-5 group-hover:text-gray-700" fill="currentColor" viewBox="0 0 24 24">
        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
      </svg>
      <span class="sr-only">Sign up with LinkedIn</span>
    </button>

    <button type="button" class="w-full inline-flex justify-center py-2.5 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-400 transition-all group">
      <svg class="w-5 h-5 group-hover:text-gray-700" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
      </svg>
      <span class="sr-only">Sign up with GitHub</span>
    </button>
  </div>

  <!-- Sign In Link -->
  <div class="mt-6 text-center">
    <p class="text-sm text-gray-600">
      Already have an account?
      <%= link_to "Sign in", new_session_path(resource_name), class: "font-medium text-blue-600 hover:text-blue-700 transition-colors" %>
    </p>
  </div>
</div>
