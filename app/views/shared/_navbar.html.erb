<!-- Premium Modern Navbar -->
<nav class="premium-navbar sticky top-0 z-50 backdrop-blur-xl bg-white/95 border-b border-gray-200/20 shadow-sm transition-all duration-300" data-controller="navigation">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-18">
      
      <!-- Enhanced <PERSON>go and Brand -->
      <div class="flex items-center group">
        <%= link_to root_path, class: "flex items-center space-x-3 transition-all duration-300 hover:scale-105" do %>
          <div class="relative">
            <svg class="h-10 w-10 text-indigo-600 transition-all duration-300 group-hover:text-indigo-700 drop-shadow-sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
            <div class="absolute -inset-1 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg blur opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
          </div>
          <div class="flex flex-col">
            <span class="text-2xl font-bold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent">AI Lead Gen</span>
            <span class="text-xs font-medium text-gray-500 -mt-1 opacity-0 group-hover:opacity-100 transition-all duration-300">Powered by Intelligence</span>
          </div>
        <% end %>
      </div>

      <!-- Enhanced Desktop Navigation -->
      <div class="hidden lg:flex items-center space-x-1">
        <% [
          { name: "Dashboard", path: root_path, icon: "M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v-2m0 0V5a2 2 0 012-2h6l2 2h6a2 2 0 012 2v2M7 13h10l-2-2m0 0l-2 2m2-2v6" },
          { name: "Keywords", path: keywords_path, icon: "M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" },
          { name: "Leads", path: leads_path, icon: "M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" },
          { name: "Integrations", path: integrations_path, icon: "M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a1 1 0 01-1-1V9a1 1 0 011-1h1a2 2 0 100-4H4a1 1 0 01-1-1V4a1 1 0 011-1h3a1 1 0 001-1z" },
          { name: "Analytics", path: analytics_path, icon: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" }
        ].each do |item| %>
          <%= link_to item[:path], class: "premium-nav-link group relative px-4 py-3 rounded-xl font-medium text-gray-700 hover:text-indigo-700 transition-all duration-300" do %>
            <div class="flex items-center space-x-2">
              <svg class="h-4 w-4 transition-all duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="<%= item[:icon] %>"></path>
              </svg>
              <span class="text-sm font-semibold"><%= item[:name] %></span>
            </div>
            <div class="absolute inset-0 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl opacity-0 group-hover:opacity-100 transition-all duration-300 -z-10"></div>
            <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-gradient-to-r from-indigo-600 to-purple-600 group-hover:w-full transition-all duration-300"></div>
          <% end %>
        <% end %>
      </div>

      <!-- Enhanced Right Side Actions -->
      <div class="flex items-center space-x-4">
        <% if user_signed_in? %>
          
          <!-- Premium Search Bar (Desktop) -->
          <div class="hidden xl:block relative">
            <div class="relative group">
              <input 
                type="search" 
                placeholder="Search leads, keywords..." 
                class="w-80 pl-10 pr-4 py-2.5 bg-gray-50/80 border-0 rounded-xl text-sm font-medium text-gray-700 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500/20 focus:bg-white transition-all duration-300"
              >
              <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 group-focus-within:text-indigo-600 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
          </div>

          <!-- Enhanced Notifications Dropdown -->
          <div class="relative" data-navigation-target="notificationsDropdown">
            <button 
              type="button" 
              class="relative p-2.5 rounded-xl text-gray-600 hover:text-indigo-600 hover:bg-indigo-50/80 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500/20 focus:bg-indigo-50/80 group"
              data-action="click->navigation#toggleNotifications"
              aria-label="View notifications">
              <svg class="h-5 w-5 transition-transform duration-200 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
              </svg>
              <%= render "shared/notifications_count", user: current_user %>
            </button>
            
            <!-- Premium Notifications Panel -->
            <div class="hidden absolute right-0 z-50 mt-3 w-96 origin-top-right rounded-2xl bg-white/95 backdrop-blur-xl border border-gray-200/20 shadow-2xl focus:outline-none transform transition-all duration-200" data-navigation-target="notificationsPanel" style="backdrop-filter: blur(20px) saturate(180%);">
              <div class="px-5 py-4 border-b border-gray-100/50">
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-semibold text-gray-900">Notifications</h3>
                  <% unread_count = current_user.notifications.unread.count %>
                  <% if unread_count > 0 %>
                    <span class="inline-flex items-center px-2.5 py-1 bg-gradient-to-r from-indigo-500 to-purple-500 text-white text-xs font-semibold rounded-full">
                      <%= unread_count %> new
                    </span>
                  <% end %>
                </div>
              </div>
              
              <div class="py-2 max-h-80 overflow-y-auto" id="notifications-list">
                <% if current_user.notifications.unread.any? %>
                  <% current_user.notifications.unread.limit(5).order(created_at: :desc).each do |notification| %>
                    <%= render "shared/notification_item", notification: notification %>
                  <% end %>
                <% else %>
                  <div class="px-5 py-8 text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                    </svg>
                    <p class="mt-2 text-sm text-gray-500">No new notifications</p>
                    <p class="text-xs text-gray-400 mt-1">You're all caught up!</p>
                  </div>
                <% end %>
              </div>
              
              <div class="px-5 py-3 border-t border-gray-100/50 bg-gray-50/30">
                <%= link_to notifications_path, class: "flex items-center justify-center text-sm font-semibold text-indigo-600 hover:text-indigo-700 transition-colors duration-150" do %>
                  <span>View all notifications</span>
                  <svg class="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                <% end %>
              </div>
            </div>
          </div>

          <!-- Enhanced User Dropdown -->
          <div class="relative" data-navigation-target="userDropdown">
            <button 
              type="button" 
              class="relative flex items-center p-2 rounded-2xl transition-all duration-200 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500/20 hover:shadow-md group"
              data-action="click->navigation#toggleUserMenu">
              <div class="flex items-center space-x-3">
                <div class="h-10 w-10 rounded-2xl bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 flex items-center justify-center text-white font-semibold text-sm ring-2 ring-white shadow-lg transition-all duration-200 group-hover:ring-4 group-hover:ring-indigo-100 group-hover:shadow-xl group-hover:scale-105">
                  <%= current_user.email[0].upcase %>
                </div>
                <div class="hidden sm:block text-left">
                  <p class="text-sm font-semibold text-gray-900 group-hover:text-indigo-700 transition-colors duration-300">
                    <%= current_user.first_name || current_user.last_name ? "#{current_user.first_name} #{current_user.last_name}".strip : "User" %>
                  </p>
                  <p class="text-xs text-gray-500 group-hover:text-gray-600 transition-colors duration-300">
                    <%= truncate(current_user.email, length: 20) %>
                  </p>
                </div>
                <svg class="h-4 w-4 text-gray-400 group-hover:text-gray-600 transition-all duration-300 group-hover:rotate-180" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </div>
            </button>
            
            <!-- Premium User Dropdown Panel -->
            <div class="hidden absolute right-0 z-50 mt-3 w-80 origin-top-right rounded-2xl bg-white/95 border border-gray-200/20 shadow-2xl focus:outline-none transform transition-all duration-200" data-navigation-target="userPanel" style="backdrop-filter: blur(20px) saturate(180%); background: rgba(255, 255, 255, 0.95);">
              
              <!-- User Info Header -->
              <div class="px-5 py-4 border-b border-gray-100/50">
                <div class="flex items-center space-x-3">
                  <div class="h-12 w-12 rounded-2xl bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 flex items-center justify-center text-white font-bold text-base ring-2 ring-white shadow-lg">
                    <%= current_user.email[0].upcase %>
                  </div>
                  <div class="flex-1 min-w-0">
                    <p class="font-semibold text-gray-900 truncate">
                      <%= current_user.first_name || current_user.last_name ? "#{current_user.first_name} #{current_user.last_name}".strip : "User" %>
                    </p>
                    <p class="text-sm text-gray-500 truncate"><%= current_user.email %></p>
                    <span class="inline-flex items-center px-2 py-0.5 bg-gradient-to-r from-green-400 to-emerald-500 text-white text-xs font-semibold rounded-full mt-1">Pro Plan</span>
                  </div>
                </div>
              </div>
              
              <!-- Menu Items -->
              <div class="py-2">
                <% [
                  { name: "My Profile", path: profile_path, icon: "M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" },
                  { name: "Settings", path: settings_path, icon: "M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z" },
                  { name: "Billing & Plans", path: billing_path, icon: "M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" }
                ].each do |item| %>
                  <%= link_to item[:path], class: "flex items-center px-5 py-3.5 text-sm text-gray-700 transition-all duration-150 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700 focus:bg-gradient-to-r focus:from-indigo-50 focus:to-purple-50 focus:text-indigo-700 focus:outline-none" do %>
                    <svg class="dropdown-item-icon mr-3 h-4 w-4 text-gray-400 transition-colors duration-150" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                      <path stroke-linecap="round" stroke-linejoin="round" d="<%= item[:icon] %>"></path>
                    </svg>
                    <span class="flex-1"><%= item[:name] %></span>
                    <svg class="ml-auto h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                  <% end %>
                <% end %>
                
                <div class="my-2 h-px bg-gradient-to-r from-transparent via-gray-200/60 to-transparent"></div>
                
                <a href="#" class="flex items-center px-5 py-3.5 text-sm text-gray-700 transition-all duration-150 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700">
                  <svg class="dropdown-item-icon mr-3 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span>Help & Support</span>
                  <span class="ml-auto text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">24/7</span>
                </a>
                
                <div class="my-2 h-px bg-gradient-to-r from-transparent via-gray-200/60 to-transparent"></div>
                
                <%= button_to destroy_user_session_path, method: :delete, class: "flex items-center px-5 py-3.5 text-sm text-gray-700 transition-all duration-150 hover:bg-red-50 hover:text-red-700 w-full text-left" do %>
                  <svg class="dropdown-item-icon mr-3 h-4 w-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                  </svg>
                  <span>Sign out</span>
                <% end %>
              </div>
            </div>
          </div>
          
        <% else %>
          <!-- Enhanced Guest Actions -->
          <div class="flex items-center space-x-3">
            <%= link_to "Sign in", new_user_session_path, class: "px-5 py-2.5 text-sm font-semibold text-gray-700 hover:text-indigo-600 transition-colors duration-200 hover:bg-gray-50 rounded-xl" %>
            <%= link_to new_user_registration_path, class: "inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-indigo-600 to-purple-600 text-white text-sm font-semibold rounded-xl shadow-lg hover:shadow-xl hover:from-indigo-700 hover:to-purple-700 transform hover:-translate-y-0.5 transition-all duration-300 group" do %>
              <span>Get Started</span>
              <svg class="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
              </svg>
            <% end %>
          </div>
        <% end %>

        <!-- Premium Mobile Menu Button -->
        <div class="lg:hidden">
          <button 
            type="button" 
            class="premium-mobile-button"
            data-action="click->navigation#toggleMobileMenu">
            <div class="mobile-hamburger">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Enhanced Mobile Menu -->
  <div class="hidden lg:hidden mobile-menu-panel" data-navigation-target="mobileMenu">
    <div class="premium-mobile-content">
      
      <!-- Mobile Search -->
      <div class="px-6 py-4 border-b border-gray-100">
        <div class="relative">
          <input 
            type="search" 
            placeholder="Search..." 
            class="w-full pl-10 pr-4 py-3 bg-gray-50 border-0 rounded-xl text-gray-700 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
          <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </div>
      </div>
      
      <!-- Mobile Navigation Links -->
      <div class="px-4 py-6 space-y-2">
        <% [
          { name: "Dashboard", path: root_path, icon: "M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v-2m0 0V5a2 2 0 012-2h6l2 2h6a2 2 0 012 2v2M7 13h10l-2-2m0 0l-2 2m2-2v6" },
          { name: "Keywords", path: keywords_path, icon: "M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" },
          { name: "Leads", path: leads_path, icon: "M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 616 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" },
          { name: "Integrations", path: integrations_path, icon: "M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a1 1 0 01-1-1V9a1 1 0 011-1h1a2 2 0 100-4H4a1 1 0 01-1-1V4a1 1 0 011-1h3a1 1 0 001-1z" },
          { name: "Analytics", path: analytics_path, icon: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" }
        ].each do |item| %>
          <%= link_to item[:path], class: "premium-mobile-nav-item" do %>
            <div class="flex items-center space-x-3">
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="<%= item[:icon] %>"></path>
              </svg>
              <span class="font-semibold"><%= item[:name] %></span>
            </div>
            <svg class="h-5 w-5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          <% end %>
        <% end %>
      </div>
      
      <% if user_signed_in? %>
        <!-- Mobile User Section -->
        <div class="px-4 py-6 border-t border-gray-100 bg-gray-50/50">
          <div class="flex items-center space-x-3 mb-4">
            <div class="premium-avatar-mobile">
              <%= current_user.email[0].upcase %>
            </div>
            <div>
              <p class="font-semibold text-gray-900">
                <%= current_user.first_name || current_user.last_name ? "#{current_user.first_name} #{current_user.last_name}".strip : "User" %>
              </p>
              <p class="text-sm text-gray-500"><%= current_user.email %></p>
            </div>
          </div>
          
          <div class="space-y-2">
            <%= link_to profile_path, class: "premium-mobile-nav-item" do %>
              <div class="flex items-center space-x-3">
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span>Profile</span>
              </div>
            <% end %>
            
            <%= link_to settings_path, class: "premium-mobile-nav-item" do %>
              <div class="flex items-center space-x-3">
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span>Settings</span>
              </div>
            <% end %>
            
            <%= link_to billing_path, class: "premium-mobile-nav-item" do %>
              <div class="flex items-center space-x-3">
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                </svg>
                <span>Billing</span>
              </div>
            <% end %>
            
            <%= button_to destroy_user_session_path, method: :delete, class: "premium-mobile-nav-item w-full text-left mt-4 border-t border-gray-200 pt-4" do %>
              <div class="flex items-center space-x-3">
                <svg class="h-5 w-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                </svg>
                <span class="text-red-700">Sign out</span>
              </div>
            <% end %>
          </div>
        </div>
      <% else %>
        <!-- Mobile Guest Actions -->
        <div class="px-4 py-6 border-t border-gray-100 space-y-3">
          <%= link_to "Sign in", new_user_session_path, class: "block w-full px-4 py-3 text-center font-semibold text-gray-700 bg-gray-100 rounded-xl hover:bg-gray-200 transition-colors duration-300" %>
          <%= link_to "Get Started", new_user_registration_path, class: "block w-full px-4 py-3 text-center font-semibold text-white bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl" %>
        </div>
      <% end %>
    </div>
  </div>
</nav>