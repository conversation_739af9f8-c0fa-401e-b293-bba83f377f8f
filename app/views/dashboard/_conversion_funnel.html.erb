<!-- Lead Conversion Funnel -->
<div class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden">
  <div class="px-6 py-4 border-b border-gray-200">
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold text-gray-900">Conversion Funnel</h3>
        <p class="text-sm text-gray-500">Track your lead conversion process from mention to customer</p>
      </div>
      <div class="flex items-center space-x-2">
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
          <%= @analytics_data[:conversions][:conversion_rate] %>% Overall Rate
        </span>
      </div>
    </div>
  </div>

  <div class="p-6">
    <!-- Funnel Visualization -->
    <div class="space-y-6">
      <% 
        funnel_data = [
          {
            name: 'Mentions Found',
            count: @analytics_data[:conversions][:mentions],
            color: 'bg-blue-500',
            icon: 'M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z',
            description: 'Social media mentions detected by your keywords'
          },
          {
            name: 'Qualified Leads',
            count: @analytics_data[:conversions][:qualified],
            color: 'bg-indigo-500',
            icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
            description: 'Mentions that passed qualification criteria'
          },
          {
            name: 'Contacted',
            count: @analytics_data[:conversions][:contacted],
            color: 'bg-purple-500',
            icon: 'M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z',
            description: 'Leads that have been reached out to'
          },
          {
            name: 'Converted',
            count: @analytics_data[:conversions][:converted],
            color: 'bg-green-500',
            icon: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1',
            description: 'Leads that became paying customers'
          }
        ]
        
        max_count = funnel_data.map { |stage| stage[:count] }.max
      %>

      <% funnel_data.each_with_index do |stage, index| %>
        <%
          percentage = max_count > 0 ? (stage[:count].to_f / max_count * 100) : 0
          prev_count = index > 0 ? funnel_data[index - 1][:count] : 1
          conversion_rate = index > 0 && prev_count > 0 ? (stage[:count].to_f / prev_count * 100).round(1) : 100
          drop_off = index > 0 ? [funnel_data[index - 1][:count] - stage[:count], 0].max : 0
        %>
        
        <div class="relative">
          <!-- Funnel Stage -->
          <div class="flex items-center space-x-6">
            <!-- Stage Icon -->
            <div class="flex-shrink-0">
              <div class="w-12 h-12 <%= stage[:color].gsub('bg-', 'bg-').gsub('-500', '-100') %> rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 <%= stage[:color].gsub('bg-', 'text-') %>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<%= stage[:icon] %>"></path>
                </svg>
              </div>
            </div>

            <!-- Stage Content -->
            <div class="flex-1">
              <div class="flex items-center justify-between mb-2">
                <div>
                  <h4 class="text-lg font-semibold text-gray-900"><%= stage[:name] %></h4>
                  <p class="text-sm text-gray-500"><%= stage[:description] %></p>
                </div>
                <div class="text-right">
                  <div class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(stage[:count]) %></div>
                  <% if index > 0 %>
                    <div class="text-sm <%= conversion_rate >= 20 ? 'text-green-600' : conversion_rate >= 10 ? 'text-yellow-600' : 'text-red-600' %>">
                      <%= conversion_rate %>% conversion
                    </div>
                  <% end %>
                </div>
              </div>

              <!-- Progress Bar -->
              <div class="w-full bg-gray-200 rounded-full h-4 overflow-hidden">
                <div class="<%= stage[:color] %> h-4 rounded-full transition-all duration-1000 ease-out relative" style="width: <%= percentage %>%">
                  <div class="absolute inset-0 bg-white bg-opacity-20 animate-pulse"></div>
                </div>
              </div>

              <!-- Stage Metrics -->
              <div class="mt-3 flex items-center justify-between text-sm">
                <div class="flex items-center space-x-4">
                  <span class="text-gray-600">
                    <span class="font-medium"><%= percentage.round(1) %>%</span> of total volume
                  </span>
                  <% if index > 0 && drop_off > 0 %>
                    <span class="text-red-600">
                      <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                      </svg>
                      <%= number_with_delimiter(drop_off) %> drop-off
                    </span>
                  <% end %>
                </div>
                
                <div class="flex items-center space-x-2">
                  <% if stage[:count] > 0 %>
                    <button class="text-indigo-600 hover:text-indigo-800 font-medium">View Details</button>
                  <% end %>
                </div>
              </div>
            </div>
          </div>

          <!-- Connector Arrow (except for last item) -->
          <% unless index == funnel_data.length - 1 %>
            <div class="flex justify-center my-4">
              <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                </svg>
              </div>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>

    <!-- Funnel Insights -->
    <div class="mt-8 pt-6 border-t border-gray-200">
      <h4 class="text-md font-semibold text-gray-900 mb-4">Conversion Insights</h4>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Best Performing Stage -->
        <div class="bg-green-50 rounded-lg p-4 border border-green-200">
          <div class="flex items-center">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
              <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
              </svg>
            </div>
            <div>
              <p class="text-sm font-medium text-green-900">Best Stage</p>
              <p class="text-lg font-bold text-green-900">
                <%
                  best_conversion = funnel_data.each_with_index.map do |stage, i|
                    next if i == 0
                    prev_count = funnel_data[i-1][:count]
                    rate = prev_count > 0 ? (stage[:count].to_f / prev_count * 100).round(1) : 0
                    { stage: stage[:name], rate: rate }
                  end.compact.select { |s| s[:rate] > 0 }.max_by { |s| s[:rate] }
                %>
                <%= best_conversion ? "#{best_conversion[:rate]}%" : "N/A" %>
              </p>
              <p class="text-xs text-green-700"><%= best_conversion ? best_conversion[:stage] : "No data" %></p>
            </div>
          </div>
        </div>

        <!-- Biggest Drop-off -->
        <div class="bg-red-50 rounded-lg p-4 border border-red-200">
          <div class="flex items-center">
            <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mr-3">
              <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
              </svg>
            </div>
            <div>
              <p class="text-sm font-medium text-red-900">Biggest Drop-off</p>
              <p class="text-lg font-bold text-red-900">
                <%
                  biggest_dropoff = funnel_data.each_with_index.map do |stage, i|
                    next if i == 0
                    dropoff_count = [funnel_data[i-1][:count] - stage[:count], 0].max
                    { stage: stage[:name], count: dropoff_count }
                  end.compact.select { |s| s[:count] > 0 }.max_by { |s| s[:count] }
                %>
                <%= biggest_dropoff ? number_with_delimiter(biggest_dropoff[:count]) : "0" %>
              </p>
              <p class="text-xs text-red-700"><%= biggest_dropoff ? "Before #{biggest_dropoff[:stage]}" : "No data" %></p>
            </div>
          </div>
        </div>

        <!-- Improvement Opportunity -->
        <div class="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
          <div class="flex items-center">
            <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
              <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
              </svg>
            </div>
            <div>
              <p class="text-sm font-medium text-yellow-900">Focus Area</p>
              <p class="text-lg font-bold text-yellow-900">
                <%
                  worst_conversion = funnel_data.each_with_index.map do |stage, i|
                    next if i == 0
                    prev_count = funnel_data[i-1][:count]
                    rate = prev_count > 0 ? (stage[:count].to_f / prev_count * 100).round(1) : 0
                    { stage: stage[:name], rate: rate }
                  end.compact.select { |s| s[:rate] >= 0 }.min_by { |s| s[:rate] }
                %>
                <%= worst_conversion ? "#{worst_conversion[:rate]}%" : "N/A" %>
              </p>
              <p class="text-xs text-yellow-700"><%= worst_conversion ? worst_conversion[:stage] : "No data" %></p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Items -->
    <div class="mt-6 bg-indigo-50 rounded-lg p-4 border border-indigo-200">
      <h5 class="text-sm font-semibold text-indigo-900 mb-2">💡 Optimization Suggestions</h5>
      <ul class="text-sm text-indigo-800 space-y-1">
        <% if @analytics_data[:conversions][:mentions] > 0 && @analytics_data[:conversions][:qualified] == 0 %>
          <li>• Consider adjusting your qualification criteria - no mentions are being qualified as leads</li>
        <% elsif @analytics_data[:conversions][:qualified] > 0 && @analytics_data[:conversions][:contacted] == 0 %>
          <li>• Start reaching out to your qualified leads to improve conversion</li>
        <% elsif @analytics_data[:conversions][:contacted] > 0 && @analytics_data[:conversions][:converted] == 0 %>
          <li>• Review your outreach messaging and follow-up strategy</li>
        <% else %>
          <li>• Focus on improving the stage with the lowest conversion rate</li>
          <li>• Consider A/B testing different outreach approaches</li>
          <li>• Monitor keyword performance to optimize mention quality</li>
        <% end %>
      </ul>
    </div>
  </div>
</div>
