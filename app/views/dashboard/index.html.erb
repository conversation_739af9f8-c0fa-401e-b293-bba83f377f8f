<% content_for :title, "Dashboard - AI Lead Generation" %>

<!-- Premium Dashboard Container with Gradient Background -->
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-indigo-50/30 to-purple-50/20 dashboard-container"
     data-controller="dashboard"
     data-dashboard-user-id="<%= current_user&.id || @user&.id %>"
     role="main"
     aria-label="AI Lead Generation Dashboard">

  <!-- Skip to main content link for screen readers -->
  <a href="#main-content"
     class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-3 rounded-xl shadow-lg z-50 font-semibold transition-all duration-200 hover:shadow-xl">
    Skip to main content
  </a>

  <!-- Premium Dashboard Header -->
  <header class="premium-dashboard-header backdrop-blur-xl bg-white/80 border-b border-indigo-200/30 shadow-sm sticky top-0 z-40" role="banner">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-20 lg:h-24">

        <!-- Header Left Section -->
        <div class="flex items-center space-x-6">
          <!-- Premium Dashboard Title -->
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg">
              <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <div>
              <h1 class="text-3xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-800 bg-clip-text text-transparent" id="dashboard-title">
                Dashboard
              </h1>
              <p class="text-sm text-gray-600 font-medium">AI-Powered Lead Generation</p>
            </div>
          </div>

          <!-- Real-time Status Indicator -->
          <div class="hidden lg:flex items-center space-x-3 px-4 py-2 bg-white/60 backdrop-blur-sm rounded-xl border border-indigo-200/40 shadow-sm" aria-live="polite" aria-label="Dashboard status">
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span class="text-sm font-medium text-gray-700">Live</span>
            </div>
            <div class="w-px h-4 bg-gray-300"></div>
            <div class="flex items-center space-x-2 text-sm text-gray-600">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span>Updated: <span data-dashboard-target="lastUpdated" class="font-medium"><%= Time.current.strftime("%I:%M %p") %></span></span>
            </div>
          </div>
        </div>

        <!-- Header Right Section -->
        <div class="flex items-center space-x-4">
          <!-- Quick Actions Toolbar -->
          <nav class="hidden md:flex items-center space-x-3" aria-label="Dashboard quick actions">
            <!-- Add Keyword Button -->
            <%= link_to new_keyword_path,
                class: "premium-action-button group inline-flex items-center px-4 py-2.5 bg-white/80 backdrop-blur-sm border border-indigo-200/50 text-sm font-semibold text-gray-700 rounded-xl shadow-sm hover:bg-white hover:border-indigo-300 hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500/20",
                aria_label: "Add new keyword for monitoring" do %>
              <svg class="w-4 h-4 mr-2 transition-transform duration-200 group-hover:rotate-90" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Add Keyword
            <% end %>

            <!-- Refresh Data Button -->
            <button data-action="click->dashboard#refreshData"
                    class="premium-cta-button group inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-500/20"
                    aria-label="Refresh dashboard data">
              <svg class="w-4 h-4 mr-2 transition-transform duration-200 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              <span data-dashboard-target="refreshText">Refresh</span>
            </button>
          </nav>

          <!-- Mobile Menu Button -->
          <button class="md:hidden p-2 rounded-xl text-gray-600 hover:text-indigo-600 hover:bg-indigo-50/80 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500/20"
                  aria-label="Open dashboard menu">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </header>

  <!-- Main Dashboard Content -->
  <main id="main-content" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

    <!-- Welcome Section with User Guidance -->
    <div class="mb-8">
      <div class="bg-gradient-to-r from-indigo-500/10 via-purple-500/10 to-pink-500/10 backdrop-blur-sm rounded-2xl border border-indigo-200/30 p-6 lg:p-8">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <h2 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">
              Welcome back, <%= current_user&.name || current_user&.email&.split('@')&.first&.humanize || 'User' %>! 👋
            </h2>
            <p class="text-gray-600 text-lg mb-4">Here's what's happening with your lead generation today.</p>

            <!-- Quick Insights -->
            <div class="flex flex-wrap items-center gap-4 text-sm">
              <div class="flex items-center space-x-2 bg-white/60 backdrop-blur-sm px-3 py-2 rounded-lg border border-indigo-200/40">
                <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                <span class="font-medium text-gray-700">All systems operational</span>
              </div>
              <div class="flex items-center space-x-2 bg-white/60 backdrop-blur-sm px-3 py-2 rounded-lg border border-indigo-200/40">
                <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                <span class="font-medium text-gray-700"><%= @analytics_data[:leads][:today] || 0 %> new leads today</span>
              </div>
            </div>
          </div>

          <!-- Quick Help Button -->
          <button class="hidden lg:flex items-center space-x-2 px-4 py-2 bg-white/80 backdrop-blur-sm border border-indigo-200/50 text-sm font-medium text-gray-700 rounded-xl hover:bg-white hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500/20"
                  title="Get help with your dashboard">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>Help</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Premium Key Metrics Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">

      <!-- Total Leads Card -->
      <div class="premium-metric-card group relative overflow-hidden bg-gradient-to-br from-white via-white to-blue-50/30 backdrop-blur-sm rounded-2xl border border-blue-200/40 shadow-sm hover:shadow-xl transition-all duration-300 cursor-pointer"
           data-tooltip="Total leads generated from all your keywords">
        <!-- Gradient Overlay -->
        <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        <div class="relative p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
            <div class="text-right">
              <div class="text-3xl font-bold text-gray-900 counter-animate" data-dashboard-target="totalLeads">
                <%= number_with_delimiter(@analytics_data[:leads][:total_leads]) %>
              </div>
              <p class="text-sm font-medium text-gray-500">Total Leads</p>
            </div>
          </div>

          <!-- Progress Indicator -->
          <div class="space-y-2">
            <% if @analytics_data[:leads][:this_month] > @analytics_data[:leads][:last_month] %>
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600">This month</span>
                <div class="flex items-center space-x-1 text-green-600 font-semibold">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                  </svg>
                  <span>+<%= ((@analytics_data[:leads][:this_month] - @analytics_data[:leads][:last_month]).to_f / [@analytics_data[:leads][:last_month], 1].max * 100).round(1) %>%</span>
                </div>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full transition-all duration-500"
                     style="width: <%= [(@analytics_data[:leads][:this_month].to_f / [@analytics_data[:leads][:this_month] + @analytics_data[:leads][:last_month], 1].max * 100), 100].min %>%"></div>
              </div>
            <% else %>
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600">This month</span>
                <span class="font-semibold text-gray-700"><%= @analytics_data[:leads][:this_month] %></span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-gradient-to-r from-blue-400 to-blue-500 h-2 rounded-full transition-all duration-500" style="width: 60%"></div>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Conversion Rate Card -->
      <div class="premium-metric-card group relative overflow-hidden bg-gradient-to-br from-white via-white to-green-50/30 backdrop-blur-sm rounded-2xl border border-green-200/40 shadow-sm hover:shadow-xl transition-all duration-300 cursor-pointer"
           data-tooltip="Percentage of mentions that convert to qualified leads">
        <!-- Gradient Overlay -->
        <div class="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        <div class="relative p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <div class="text-right">
              <div class="text-3xl font-bold text-gray-900 counter-animate" data-dashboard-target="conversionRate">
                <%= @analytics_data[:conversions][:conversion_rate] %>%
              </div>
              <p class="text-sm font-medium text-gray-500">Conversion Rate</p>
            </div>
          </div>

          <!-- Conversion Details -->
          <div class="space-y-2">
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-600">Converted</span>
              <span class="font-semibold text-gray-700"><%= @analytics_data[:conversions][:converted] %></span>
            </div>
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-600">Total mentions</span>
              <span class="font-semibold text-gray-700"><%= @analytics_data[:conversions][:mentions] %></span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mt-3">
              <div class="bg-gradient-to-r from-green-400 to-emerald-500 h-2 rounded-full transition-all duration-500"
                   style="width: <%= [@analytics_data[:conversions][:conversion_rate], 100].min %>%"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Active Keywords Card -->
      <div class="premium-metric-card group relative overflow-hidden bg-gradient-to-br from-white via-white to-purple-50/30 backdrop-blur-sm rounded-2xl border border-purple-200/40 shadow-sm hover:shadow-xl transition-all duration-300 cursor-pointer"
           data-tooltip="Number of keywords actively monitoring for mentions">
        <!-- Gradient Overlay -->
        <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        <div class="relative p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
              </svg>
            </div>
            <div class="text-right">
              <div class="text-3xl font-bold text-gray-900 counter-animate" data-dashboard-target="activeKeywords">
                <%= number_with_delimiter(@user_keywords&.count || 0) %>
              </div>
              <p class="text-sm font-medium text-gray-500">Active Keywords</p>
            </div>
          </div>

          <!-- Keywords Performance -->
          <div class="space-y-2">
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-600">Performing well</span>
              <span class="font-semibold text-green-600"><%= @keyword_performance.count %></span>
            </div>
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-600">Need attention</span>
              <span class="font-semibold text-yellow-600"><%= [(@user_keywords&.count || 0) - @keyword_performance.count, 0].max %></span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mt-3">
              <div class="bg-gradient-to-r from-purple-400 to-pink-500 h-2 rounded-full transition-all duration-500"
                   style="width: <%= (@user_keywords&.count || 0) > 0 ? (@keyword_performance.count.to_f / @user_keywords.count * 100) : 0 %>%"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Integrations Card -->
      <div class="premium-metric-card group relative overflow-hidden bg-gradient-to-br from-white via-white to-indigo-50/30 backdrop-blur-sm rounded-2xl border border-indigo-200/40 shadow-sm hover:shadow-xl transition-all duration-300 cursor-pointer"
           data-tooltip="Connected social media platforms and monitoring tools">
        <!-- Gradient Overlay -->
        <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        <div class="relative p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
              </svg>
            </div>
            <div class="text-right">
              <div class="text-3xl font-bold text-gray-900 counter-animate" data-dashboard-target="activeIntegrations">
                <%= @integration_status.where(status: 'active').count %>
              </div>
              <p class="text-sm font-medium text-gray-500">Active Integrations</p>
            </div>
          </div>

          <!-- Integration Status -->
          <div class="space-y-2">
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-600">Connected</span>
              <span class="font-semibold text-gray-700"><%= @integration_status.count %></span>
            </div>
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-600">Health score</span>
              <span class="font-semibold text-green-600">98%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mt-3">
              <div class="bg-gradient-to-r from-indigo-400 to-blue-500 h-2 rounded-full transition-all duration-500"
                   style="width: <%= @integration_status.count > 0 ? (@integration_status.where(status: 'active').count.to_f / @integration_status.count * 100) : 0 %>%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Premium Main Content Grid -->
    <div class="grid grid-cols-1 xl:grid-cols-12 gap-8">

      <!-- Left Column - Quick Analytics Overview (8 columns) -->
      <div class="xl:col-span-8 space-y-8">

        <!-- Quick Analytics Summary -->
        <div class="premium-widget-card bg-gradient-to-br from-white via-white to-indigo-50/20 backdrop-blur-sm rounded-2xl border border-indigo-200/30 shadow-lg overflow-hidden">
          <div class="premium-widget-header bg-gradient-to-r from-indigo-500/10 to-purple-500/10 px-6 py-5 border-b border-indigo-200/30">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-xl font-bold text-gray-900">Performance Overview</h3>
                <p class="text-sm text-gray-600 mt-1">Your lead generation at a glance</p>
              </div>
              <%= link_to dashboard_analytics_path, 
                  class: "premium-action-button inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white text-sm font-semibold rounded-xl shadow-sm hover:shadow-md transition-all duration-200" do %>
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                View Full Analytics
              <% end %>
            </div>
          </div>

          <!-- Simple Performance Chart -->
          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <!-- Mini Performance Cards -->
              <div class="text-center p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl">
                <div class="text-3xl font-bold text-indigo-600 mb-2"><%= @analytics_data[:leads][:today] || 0 %></div>
                <div class="text-sm text-gray-600">New Leads Today</div>
              </div>
              <div class="text-center p-4 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl">
                <div class="text-3xl font-bold text-green-600 mb-2"><%= @analytics_data[:conversions][:conversion_rate] %>%</div>
                <div class="text-sm text-gray-600">Conversion Rate</div>
              </div>
              <div class="text-center p-4 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl">
                <div class="text-3xl font-bold text-purple-600 mb-2"><%= @analytics_data[:leads][:growth_rate] %>%</div>
                <div class="text-sm text-gray-600">Monthly Growth</div>
              </div>
            </div>

            <!-- Simple Trend Line -->
            <div class="h-48 bg-gray-50 rounded-xl p-4">
              <div class="flex items-center justify-between mb-4">
                <h4 class="text-sm font-medium text-gray-700">7-Day Lead Trend</h4>
                <div class="flex items-center space-x-2 text-xs text-gray-500">
                  <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span>Leads</span>
                </div>
              </div>
              <div class="h-32 flex items-end justify-between space-x-2">
                <% last_7_days = @analytics_data[:leads][:daily_leads].to_a.last(7) %>
                <% max_value = last_7_days.map { |_, v| v }.max.to_f %>
                <% max_value = 1 if max_value == 0 %>
                <% last_7_days.each do |date, count| %>
                  <div class="flex-1 bg-gradient-to-t from-blue-500 to-indigo-500 rounded-t-lg hover:from-blue-600 hover:to-indigo-600 transition-all duration-200"
                       style="height: <%= (count.to_f / max_value * 100).round %>%"
                       title="<%= date.strftime('%b %d') %>: <%= count %> leads">
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Activity Feed -->
        <div class="premium-widget-card bg-gradient-to-br from-white via-white to-green-50/20 backdrop-blur-sm rounded-2xl border border-green-200/30 shadow-lg overflow-hidden">
          <div class="premium-widget-header bg-gradient-to-r from-green-500/10 to-emerald-500/10 px-6 py-5 border-b border-green-200/30">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-xl font-bold text-gray-900">Recent Activity</h3>
                <p class="text-sm text-gray-600 mt-1">Latest updates from your lead generation system</p>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span class="text-xs text-green-600 font-medium">Live</span>
              </div>
            </div>
          </div>

          <!-- Activity List -->
          <div class="p-6">
            <div class="space-y-4">
              <% if @recent_leads.any? %>
                <% @recent_leads.first(5).each do |lead| %>
                  <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-xl hover:bg-gray-100 transition-all duration-200">
                    <div class="flex-shrink-0">
                      <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                      </div>
                    </div>
                    <div class="flex-1">
                      <p class="text-sm font-medium text-gray-900">New lead: <%= lead.name || 'Unknown' %></p>
                      <p class="text-xs text-gray-600">From keyword: <%= lead.mention&.keyword&.keyword %></p>
                      <p class="text-xs text-gray-500"><%= time_ago_in_words(lead.created_at) %> ago</p>
                    </div>
                  </div>
                <% end %>
              <% else %>
                <div class="text-center py-8 text-gray-500">
                  <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <p class="text-sm">No recent activity</p>
                  <p class="text-xs text-gray-400 mt-1">New activity will appear here</p>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column - Widgets and Quick Info (4 columns) -->
      <div class="xl:col-span-4 space-y-6">

        <!-- Enhanced Recent Leads Widget -->
        <div class="premium-widget-card bg-gradient-to-br from-white via-white to-green-50/20 backdrop-blur-sm rounded-2xl border border-green-200/30 shadow-lg overflow-hidden">
          <div class="premium-widget-header bg-gradient-to-r from-green-500/10 to-emerald-500/10 px-6 py-4 border-b border-green-200/30">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-bold text-gray-900">Recent Leads</h3>
                <p class="text-sm text-gray-600">Latest qualified prospects</p>
              </div>
              <button class="text-sm font-medium text-indigo-600 hover:text-indigo-800 transition-colors duration-200">
                View All
              </button>
            </div>
          </div>
          <div class="premium-widget-content max-h-96 overflow-y-auto" data-dashboard-target="recentLeads">
            <!-- Recent leads will be rendered here with enhanced styling -->
            <div class="p-6 text-center text-gray-500">
              <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
              <p class="text-sm">No recent leads yet</p>
              <p class="text-xs text-gray-400 mt-1">New leads will appear here as they're generated</p>
            </div>
          </div>
        </div>

        <!-- Enhanced Keyword Performance Widget -->
        <div class="premium-widget-card bg-gradient-to-br from-white via-white to-blue-50/20 backdrop-blur-sm rounded-2xl border border-blue-200/30 shadow-lg overflow-hidden">
          <div class="premium-widget-header bg-gradient-to-r from-blue-500/10 to-indigo-500/10 px-6 py-4 border-b border-blue-200/30">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-bold text-gray-900">Top Keywords</h3>
                <p class="text-sm text-gray-600">Best performing search terms</p>
              </div>
              <button class="text-sm font-medium text-indigo-600 hover:text-indigo-800 transition-colors duration-200">
                Manage
              </button>
            </div>
          </div>
          <div class="premium-widget-content p-6" data-dashboard-target="keywordPerformance">
            <!-- Keyword performance will be rendered here with enhanced styling -->
            <div class="text-center text-gray-500">
              <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
              </svg>
              <p class="text-sm">No keyword data available</p>
              <p class="text-xs text-gray-400 mt-1">Add keywords to start monitoring</p>
            </div>
          </div>
        </div>

        <!-- Enhanced Integration Status -->
        <%= render 'integration_status' %>

        <!-- Enhanced Notification Center -->
        <%= render 'notification_center' %>
      </div>
    </div>
  </main>
</div>
