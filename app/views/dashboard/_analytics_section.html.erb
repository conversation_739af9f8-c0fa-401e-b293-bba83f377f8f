<!-- Premium Analytics Overview Section -->
<div class="premium-spacing">

  <!-- Analytics Summary Cards - Expanded Layout -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">

    <!-- Performance Score Card - Expanded -->
    <div class="premium-metric-card group relative overflow-hidden bg-gradient-to-br from-white via-white to-emerald-50/30 backdrop-blur-sm rounded-3xl border border-emerald-200/40 shadow-lg hover:shadow-2xl transition-all duration-300">
      <div class="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-green-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      <div class="relative p-8 lg:p-10">
        <div class="flex items-center justify-between mb-6">
          <div class="w-16 h-16 bg-gradient-to-br from-emerald-500 to-green-600 rounded-3xl flex items-center justify-center shadow-xl group-hover:shadow-2xl group-hover:scale-110 transition-all duration-300">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          <div class="text-right">
            <div class="text-5xl font-bold text-gray-900 counter-animate mb-2">92</div>
            <p class="text-base font-semibold text-gray-500">Performance Score</p>
            <p class="text-sm text-emerald-600 font-medium">Excellent</p>
          </div>
        </div>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-base text-gray-600">This week</span>
            <div class="flex items-center space-x-2 text-emerald-600 font-bold">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
              </svg>
              <span class="text-lg">+8 points</span>
            </div>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-3">
            <div class="bg-gradient-to-r from-emerald-400 to-green-500 h-3 rounded-full transition-all duration-1000 shadow-sm" style="width: 92%"></div>
          </div>
          <div class="flex justify-between text-sm text-gray-500">
            <span>Poor</span>
            <span>Good</span>
            <span>Excellent</span>
          </div>
        </div>
      </div>
    </div>

    <!-- AI Insights Card - Expanded -->
    <div class="premium-metric-card group relative overflow-hidden bg-gradient-to-br from-white via-white to-purple-50/30 backdrop-blur-sm rounded-3xl border border-purple-200/40 shadow-lg hover:shadow-2xl transition-all duration-300">
      <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      <div class="relative p-8 lg:p-10">
        <div class="flex items-center justify-between mb-6">
          <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-3xl flex items-center justify-center shadow-xl group-hover:shadow-2xl group-hover:scale-110 transition-all duration-300">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
          </div>
          <div class="text-right">
            <div class="text-5xl font-bold text-gray-900 counter-animate mb-2">7</div>
            <p class="text-base font-semibold text-gray-500">AI Insights</p>
            <p class="text-sm text-purple-600 font-medium">Active</p>
          </div>
        </div>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-base text-gray-600">New recommendations</span>
            <span class="font-bold text-purple-600 text-lg">3 urgent</span>
          </div>
          <div class="flex items-center space-x-3 bg-purple-50/50 rounded-xl p-3">
            <div class="w-3 h-3 bg-purple-400 rounded-full animate-pulse"></div>
            <span class="text-sm text-purple-700 font-medium">AI analysis running</span>
            <div class="ml-auto">
              <div class="flex space-x-1">
                <div class="w-1 h-1 bg-purple-400 rounded-full animate-bounce"></div>
                <div class="w-1 h-1 bg-purple-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                <div class="w-1 h-1 bg-purple-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Trend Analysis Card - Expanded -->
    <div class="premium-metric-card group relative overflow-hidden bg-gradient-to-br from-white via-white to-blue-50/30 backdrop-blur-sm rounded-3xl border border-blue-200/40 shadow-lg hover:shadow-2xl transition-all duration-300">
      <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      <div class="relative p-8 lg:p-10">
        <div class="flex items-center justify-between mb-6">
          <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-3xl flex items-center justify-center shadow-xl group-hover:shadow-2xl group-hover:scale-110 transition-all duration-300">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
          </div>
          <div class="text-right">
            <div class="text-5xl mb-2">📈</div>
            <p class="text-base font-semibold text-gray-500">Trending Up</p>
            <p class="text-sm text-blue-600 font-medium">Strong Growth</p>
          </div>
        </div>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-base text-gray-600">Growth rate</span>
            <div class="flex items-center space-x-2 text-blue-600 font-bold">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
              </svg>
              <span class="text-lg">+24.3%</span>
            </div>
          </div>
          <div class="bg-blue-50/50 rounded-xl p-3">
            <div class="text-sm text-blue-700 font-medium">🏆 Best month this quarter</div>
            <div class="text-xs text-blue-600 mt-1">Outperforming previous months by 18%</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Enhanced Analytics Charts Grid - Expanded Layout -->
  <div class="grid grid-cols-1 xl:grid-cols-5 gap-8 mb-12">

    <!-- Main Leads Trend Chart (Much Larger) -->
    <div class="xl:col-span-3 premium-widget-card bg-gradient-to-br from-white via-white to-blue-50/20 backdrop-blur-sm rounded-3xl border border-blue-200/30 shadow-xl overflow-hidden">
      <div class="premium-widget-header bg-gradient-to-r from-blue-500/10 to-indigo-500/10 px-8 py-6 border-b border-blue-200/30">
        <div class="flex items-center justify-between">
          <div>
            <h4 class="text-2xl font-bold text-gray-900">📊 Lead Generation Analytics</h4>
            <p class="text-base text-gray-600 mt-2">Comprehensive view of your lead acquisition performance over time</p>
          </div>
          <div class="flex items-center space-x-3">
            <!-- Chart Type Selector -->
            <div class="flex items-center space-x-1 bg-white/60 backdrop-blur-sm rounded-lg p-1 border border-blue-200/40">
              <button class="px-3 py-1.5 text-xs font-medium text-blue-600 bg-blue-100 rounded-md transition-all duration-200">
                Area
              </button>
              <button class="px-3 py-1.5 text-xs font-medium text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-all duration-200">
                Line
              </button>
              <button class="px-3 py-1.5 text-xs font-medium text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-all duration-200">
                Bar
              </button>
            </div>

            <!-- Performance Badge -->
            <span class="inline-flex items-center px-3 py-1.5 bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-semibold rounded-full shadow-sm">
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
              </svg>
              +12.5% vs last period
            </span>
          </div>
        </div>
      </div>
      <div class="premium-widget-content p-6">
        <div class="chart-container h-80"
             data-controller="chart"
             data-chart-type-value="area"
             data-chart-data-value='<%= {
               labels: @analytics_data[:leads][:daily_leads].keys.map { |date| date.strftime("%m/%d") },
               datasets: [{
                 label: "Leads Generated",
                 data: @analytics_data[:leads][:daily_leads].values,
                 borderColor: "rgb(59, 130, 246)",
                 backgroundColor: "rgba(59, 130, 246, 0.1)",
                 fill: true,
                 tension: 0.4,
                 pointBackgroundColor: "rgb(59, 130, 246)",
                 pointBorderColor: "#fff",
                 pointBorderWidth: 2,
                 pointRadius: 4,
                 pointHoverRadius: 6
               }, {
                 label: "Qualified Leads",
                 data: @analytics_data[:leads][:daily_leads].values.map { |v| (v * 0.3).round },
                 borderColor: "rgb(139, 92, 246)",
                 backgroundColor: "rgba(139, 92, 246, 0.1)",
                 fill: true,
                 tension: 0.4,
                 pointBackgroundColor: "rgb(139, 92, 246)",
                 pointBorderColor: "#fff",
                 pointBorderWidth: 2,
                 pointRadius: 4,
                 pointHoverRadius: 6
               }]
             }.to_json %>'
             data-chart-target="container">
          <!-- Enhanced Chart Loading State -->
          <div class="flex items-center justify-center h-full text-gray-500">
            <div class="text-center">
              <div class="relative">
                <div class="w-16 h-16 mx-auto mb-4">
                  <div class="absolute inset-0 border-4 border-blue-200 rounded-full"></div>
                  <div class="absolute inset-0 border-4 border-blue-500 rounded-full border-t-transparent animate-spin"></div>
                </div>
              </div>
              <p class="text-sm font-medium">Loading analytics data...</p>
              <p class="text-xs text-gray-400 mt-1">Analyzing your lead generation performance</p>
            </div>
          </div>
        </div>

        <!-- Chart Legend and Stats -->
        <div class="mt-6 pt-6 border-t border-gray-200/50">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600"><%= @analytics_data[:leads][:total_leads] %></div>
              <div class="text-xs text-gray-500">Total Leads</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-purple-600"><%= (@analytics_data[:leads][:total_leads] * 0.3).round %></div>
              <div class="text-xs text-gray-500">Qualified</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600"><%= @analytics_data[:conversions][:conversion_rate] %>%</div>
              <div class="text-xs text-gray-500">Conversion Rate</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-orange-600">$<%= number_with_delimiter((@analytics_data[:leads][:total_leads] * 150).round) %></div>
              <div class="text-xs text-gray-500">Est. Value</div>
            </div>
          </div>
        </div>
      </div>
    </div>
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          Last 30 Days
          <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
        
        <div data-dropdown-target="menu" class="hidden absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          <div class="py-1">
            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Last 7 Days</a>
            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Last 30 Days</a>
            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Last 90 Days</a>
            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Custom Range</a>
          </div>
        </div>
      </div>
      
      <!-- Export Button -->
      <button class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        Export
      </button>
    </div>
  </div>

  <!-- Main Analytics Charts -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- Lead Generation Trend -->
    <div class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Lead Generation Trend</h3>
            <p class="text-sm text-gray-500">Daily leads over time</p>
          </div>
          <div class="flex items-center space-x-2">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
              </svg>
              +12.5%
            </span>
          </div>
        </div>
      </div>
      <div class="p-6">
        <div data-controller="chart" 
             data-chart-type-value="area"
             data-chart-data-value='<%= {
               labels: @analytics_data[:leads][:daily_leads].keys.map { |date| date.strftime("%m/%d") },
               datasets: [{
                 label: "Leads",
                 data: @analytics_data[:leads][:daily_leads].values
               }]
             }.to_json %>'
             data-chart-target="container">
        </div>
      </div>
    </div>

    <!-- Right Sidebar Analytics -->
    <div class="space-y-6">

      <!-- Conversion Funnel Widget -->
      <div class="premium-widget-card bg-gradient-to-br from-white via-white to-purple-50/20 backdrop-blur-sm rounded-2xl border border-purple-200/30 shadow-lg overflow-hidden">
        <div class="premium-widget-header bg-gradient-to-r from-purple-500/10 to-pink-500/10 px-6 py-4 border-b border-purple-200/30">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-lg font-bold text-gray-900">Conversion Funnel</h4>
              <p class="text-sm text-gray-600">Lead progression stages</p>
            </div>
            <div class="text-right">
              <div class="text-lg font-bold text-purple-600"><%= @analytics_data[:conversions][:conversion_rate] %>%</div>
              <p class="text-xs text-gray-500">Overall rate</p>
            </div>
          </div>
        </div>
        <div class="premium-widget-content p-6">
          <!-- Funnel Visualization -->
          <div class="space-y-4">
            <%
              funnel_stages = [
                { name: "Mentions", count: @analytics_data[:conversions][:mentions], color: "bg-blue-500", percentage: 100 },
                { name: "Qualified", count: @analytics_data[:conversions][:qualified], color: "bg-indigo-500", percentage: (@analytics_data[:conversions][:qualified].to_f / [@analytics_data[:conversions][:mentions], 1].max * 100).round },
                { name: "Contacted", count: @analytics_data[:conversions][:contacted], color: "bg-purple-500", percentage: (@analytics_data[:conversions][:contacted].to_f / [@analytics_data[:conversions][:mentions], 1].max * 100).round },
                { name: "Converted", count: @analytics_data[:conversions][:converted], color: "bg-green-500", percentage: (@analytics_data[:conversions][:converted].to_f / [@analytics_data[:conversions][:mentions], 1].max * 100).round }
              ]
            %>

            <% funnel_stages.each_with_index do |stage, index| %>
              <div class="relative">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center space-x-3">
                    <div class="w-3 h-3 <%= stage[:color] %> rounded-full"></div>
                    <span class="text-sm font-medium text-gray-700"><%= stage[:name] %></span>
                  </div>
                  <div class="text-right">
                    <div class="text-sm font-bold text-gray-900"><%= number_with_delimiter(stage[:count]) %></div>
                    <div class="text-xs text-gray-500"><%= stage[:percentage] %>%</div>
                  </div>
                </div>

                <!-- Funnel Bar -->
                <div class="relative h-8 bg-gray-100 rounded-lg overflow-hidden">
                  <div class="absolute inset-0 <%= stage[:color] %> opacity-20 transition-all duration-1000"
                       style="width: <%= stage[:percentage] %>%"></div>
                  <div class="absolute inset-0 <%= stage[:color] %> transition-all duration-1000"
                       style="width: <%= [stage[:percentage] * 0.7, 100].min %>%"></div>
                </div>

                <!-- Conversion Rate -->
                <% if index > 0 %>
                  <div class="absolute -right-2 top-1/2 transform -translate-y-1/2">
                    <div class="bg-white border border-gray-200 rounded-full px-2 py-1 text-xs font-medium text-gray-600 shadow-sm">
                      <% prev_count = funnel_stages[index-1][:count] %>
                      <%= prev_count > 0 ? ((stage[:count].to_f / prev_count) * 100).round(1) : 0 %>%
                    </div>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>

          <!-- Funnel Insights -->
          <div class="mt-6 pt-4 border-t border-gray-200/50">
            <div class="grid grid-cols-2 gap-4 text-center">
              <div>
                <div class="text-lg font-bold text-green-600">
                  <% first_count = funnel_stages.first[:count] %>
                  <%= first_count > 0 ? ((funnel_stages.last[:count].to_f / first_count) * 100).round(1) : 0 %>%
                </div>
                <div class="text-xs text-gray-500">End-to-end conversion</div>
              </div>
              <div>
                <div class="text-lg font-bold text-blue-600">
                  <%= funnel_stages.map { |s| s[:count] }.sum %>
                </div>
                <div class="text-xs text-gray-500">Total pipeline</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Top Keywords Performance -->
      <div class="premium-widget-card bg-gradient-to-br from-white via-white to-orange-50/20 backdrop-blur-sm rounded-2xl border border-orange-200/30 shadow-lg overflow-hidden">
        <div class="premium-widget-header bg-gradient-to-r from-orange-500/10 to-yellow-500/10 px-6 py-4 border-b border-orange-200/30">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-lg font-bold text-gray-900">Top Keywords</h4>
              <p class="text-sm text-gray-600">Best performing search terms</p>
            </div>
            <button class="text-sm font-medium text-orange-600 hover:text-orange-800 transition-colors duration-200">
              View All
            </button>
          </div>
        </div>
        <div class="premium-widget-content p-6">
          <div class="space-y-4">
            <% @analytics_data[:keywords].first(5).each_with_index do |keyword, index| %>
              <div class="flex items-center justify-between p-3 bg-gradient-to-r from-orange-50/50 to-yellow-50/50 rounded-xl border border-orange-200/30 hover:shadow-md transition-all duration-200">
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-gradient-to-br from-orange-400 to-yellow-500 rounded-lg flex items-center justify-center text-white text-sm font-bold">
                    <%= index + 1 %>
                  </div>
                  <div>
                    <div class="font-medium text-gray-900 text-sm"><%= keyword[:name] %></div>
                    <div class="text-xs text-gray-500"><%= keyword[:mentions] %> mentions</div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-sm font-bold text-orange-600"><%= keyword[:conversion_rate] %>%</div>
                  <div class="text-xs text-gray-500">conversion</div>
                </div>
              </div>
            <% end %>
          </div>

          <!-- Keywords Summary -->
          <div class="mt-6 pt-4 border-t border-gray-200/50">
            <div class="text-center">
              <div class="text-lg font-bold text-gray-900"><%= @analytics_data[:keywords].count %></div>
              <div class="text-xs text-gray-500">Total active keywords</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Platform Performance -->
      <div class="premium-widget-card bg-gradient-to-br from-white via-white to-cyan-50/20 backdrop-blur-sm rounded-2xl border border-cyan-200/30 shadow-lg overflow-hidden">
        <div class="premium-widget-header bg-gradient-to-r from-cyan-500/10 to-blue-500/10 px-6 py-4 border-b border-cyan-200/30">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-lg font-bold text-gray-900">Platform Insights</h4>
              <p class="text-sm text-gray-600">Performance by social platform</p>
            </div>
          </div>
        </div>
        <div class="premium-widget-content p-6">
          <div class="space-y-4">
            <%
              platforms = [
                { name: "LinkedIn", mentions: 45, leads: 12, color: "bg-blue-600", icon: "💼" },
                { name: "Twitter", mentions: 38, leads: 8, color: "bg-sky-500", icon: "🐦" },
                { name: "Reddit", mentions: 22, leads: 5, color: "bg-orange-600", icon: "🔗" }
              ]
            %>

            <% platforms.each do |platform| %>
              <div class="flex items-center justify-between p-3 bg-gradient-to-r from-cyan-50/50 to-blue-50/50 rounded-xl border border-cyan-200/30 hover:shadow-md transition-all duration-200">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 <%= platform[:color] %> rounded-xl flex items-center justify-center text-white text-lg">
                    <%= platform[:icon] %>
                  </div>
                  <div>
                    <div class="font-medium text-gray-900 text-sm"><%= platform[:name] %></div>
                    <div class="text-xs text-gray-500"><%= platform[:mentions] %> mentions</div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-sm font-bold text-cyan-600"><%= platform[:leads] %></div>
                  <div class="text-xs text-gray-500">leads</div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Detailed Analytics Grid -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Keyword Performance Chart -->
    <div class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Keyword Performance</h3>
        <p class="text-sm text-gray-500">Top performing keywords</p>
      </div>
      <div class="p-6">
        <div data-controller="chart" 
             data-chart-type-value="bar"
             data-chart-data-value='<%= {
               labels: @analytics_data[:keywords].first(5).map { |k| k[:name].truncate(10) },
               datasets: [{
                 label: "Conversion Rate",
                 data: @analytics_data[:keywords].first(5).map { |k| k[:conversion_rate] }
               }]
             }.to_json %>'
             data-chart-target="container">
        </div>
      </div>
    </div>

    <!-- Response Time Analytics -->
    <div class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Response Time</h3>
        <p class="text-sm text-gray-500">Average response metrics</p>
      </div>
      <div class="p-6 space-y-6">
        <!-- Average Response Time -->
        <div class="text-center">
          <div class="text-3xl font-bold text-indigo-600"><%= @conversion_metrics[:avg_response_time] %>h</div>
          <div class="text-sm text-gray-500">Average Response Time</div>
        </div>
        
        <!-- Response Time Distribution -->
        <div class="space-y-3">
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">< 1 hour</span>
            <div class="flex-1 mx-3">
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-green-500 h-2 rounded-full" style="width: 45%"></div>
              </div>
            </div>
            <span class="text-sm font-medium text-gray-900">45%</span>
          </div>
          
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">1-4 hours</span>
            <div class="flex-1 mx-3">
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-yellow-500 h-2 rounded-full" style="width: 30%"></div>
              </div>
            </div>
            <span class="text-sm font-medium text-gray-900">30%</span>
          </div>
          
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">4+ hours</span>
            <div class="flex-1 mx-3">
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-red-500 h-2 rounded-full" style="width: 25%"></div>
              </div>
            </div>
            <span class="text-sm font-medium text-gray-900">25%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Platform Performance -->
    <div class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Platform Performance</h3>
        <p class="text-sm text-gray-500">Leads by platform</p>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          <% @analytics_data[:integrations].each do |integration| %>
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                  <% case integration[:platform].downcase %>
                  <% when 'twitter' %>
                    <svg class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                    </svg>
                  <% when 'linkedin' %>
                    <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                    </svg>
                  <% else %>
                    <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                    </svg>
                  <% end %>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900"><%= integration[:platform].capitalize %></p>
                  <p class="text-xs text-gray-500"><%= integration[:mentions_count] %> mentions</p>
                </div>
              </div>
              <div class="text-right">
                <div class="flex items-center space-x-2">
                  <div class="w-12 bg-gray-200 rounded-full h-2">
                    <div class="bg-indigo-500 h-2 rounded-full" style="width: <%= [integration[:health_score], 100].min %>%"></div>
                  </div>
                  <span class="text-xs font-medium text-gray-900"><%= integration[:health_score] %>%</span>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Real-time Activity Feed -->
  <div class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-semibold text-gray-900">Real-time Activity</h3>
          <p class="text-sm text-gray-500">Live feed of lead generation activities</p>
        </div>
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span class="text-xs text-gray-500">Live</span>
        </div>
      </div>
    </div>
    <div class="p-6">
      <div class="space-y-4" data-dashboard-target="activityFeed">
        <!-- Activity items will be populated by JavaScript -->
        <div class="flex items-start space-x-3">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
          </div>
          <div class="flex-1">
            <p class="text-sm text-gray-900">New lead detected from keyword "Ruby on Rails developer"</p>
            <p class="text-xs text-gray-500">2 minutes ago</p>
          </div>
        </div>
        
        <div class="flex items-start space-x-3">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="flex-1">
            <p class="text-sm text-gray-900">Lead converted to customer</p>
            <p class="text-xs text-gray-500">5 minutes ago</p>
          </div>
        </div>
        
        <div class="flex items-start space-x-3">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
              </svg>
            </div>
          </div>
          <div class="flex-1">
            <p class="text-sm text-gray-900">Twitter integration synced successfully</p>
            <p class="text-xs text-gray-500">10 minutes ago</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Analytics Action Center -->
  <div class="mt-8 premium-widget-card bg-gradient-to-br from-white via-white to-indigo-50/20 backdrop-blur-sm rounded-2xl border border-indigo-200/30 shadow-lg overflow-hidden">
    <div class="premium-widget-header bg-gradient-to-r from-indigo-500/10 to-purple-500/10 px-6 py-5 border-b border-indigo-200/30">
      <div class="flex items-center justify-between">
        <div>
          <h4 class="text-xl font-bold text-gray-900">🎯 Analytics Summary & Actions</h4>
          <p class="text-sm text-gray-600">Your lead generation performance is <span class="font-semibold text-green-600">above average</span> this month</p>
        </div>
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span class="text-xs text-green-600 font-medium">Performing Well</span>
        </div>
      </div>
    </div>
    <div class="premium-widget-content p-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">

        <!-- Export Report Action -->
        <button class="premium-action-card group flex flex-col items-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200/50 rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-300">
          <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-200 shadow-lg">
            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <h5 class="font-bold text-gray-900 text-base mb-2">Export Analytics</h5>
          <p class="text-sm text-gray-600 text-center leading-relaxed">Download comprehensive performance report with insights and recommendations</p>
          <div class="mt-3 text-xs text-blue-600 font-medium">PDF • Excel • CSV</div>
        </button>

        <!-- AI Optimization Action -->
        <button class="premium-action-card group flex flex-col items-center p-6 bg-gradient-to-br from-purple-50 to-pink-50 border border-purple-200/50 rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-300">
          <div class="w-14 h-14 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-200 shadow-lg">
            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
          </div>
          <h5 class="font-bold text-gray-900 text-base mb-2">AI Optimization</h5>
          <p class="text-sm text-gray-600 text-center leading-relaxed">Get personalized AI-powered suggestions to improve your conversion rates</p>
          <div class="mt-3 text-xs text-purple-600 font-medium">7 new insights available</div>
        </button>

        <!-- Expand Coverage Action -->
        <button class="premium-action-card group flex flex-col items-center p-6 bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200/50 rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-300">
          <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-200 shadow-lg">
            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
          </div>
          <h5 class="font-bold text-gray-900 text-base mb-2">Expand Coverage</h5>
          <p class="text-sm text-gray-600 text-center leading-relaxed">Add more keywords and platforms to capture additional opportunities</p>
          <div class="mt-3 text-xs text-green-600 font-medium">Recommended: +5 keywords</div>
        </button>
      </div>

      <!-- Performance Summary Bar -->
      <div class="mt-8 pt-6 border-t border-gray-200/50">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="text-center">
              <div class="text-2xl font-bold text-gray-900"><%= @analytics_data[:leads][:total_leads] %></div>
              <div class="text-xs text-gray-500">Total Leads</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600"><%= @analytics_data[:conversions][:conversion_rate] %>%</div>
              <div class="text-xs text-gray-500">Conversion Rate</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600"><%= @analytics_data[:keywords].count %></div>
              <div class="text-xs text-gray-500">Active Keywords</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-purple-600">92</div>
              <div class="text-xs text-gray-500">Performance Score</div>
            </div>
          </div>

          <div class="text-right">
            <div class="text-sm text-gray-600">Next analysis in</div>
            <div class="text-lg font-bold text-indigo-600">2h 15m</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
