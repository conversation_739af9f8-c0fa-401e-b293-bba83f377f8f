<!-- Hero Section with Modern Design -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-b from-white to-gray-50">
  <!-- Geometric Pattern Background -->
  <div class="absolute inset-0 opacity-5">
    <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width="60" height="60" xmlns="http://www.w3.org/2000/svg"%3E%3Cdefs%3E%3Cpattern id="grid" width="60" height="60" patternUnits="userSpaceOnUse"%3E%3Cpath d="M 60 0 L 0 0 0 60" fill="none" stroke="%23000000" stroke-width="0.5"/%3E%3C/pattern%3E%3C/defs%3E%3Crect width="100%25" height="100%25" fill="url(%23grid)"/%3E%3C/svg%3E');"></div>
  </div>
  
  <!-- Floating Elements -->
  <div class="absolute top-20 left-10 w-20 h-20 bg-gradient-to-br from-blue-400 to-indigo-600 rounded-full opacity-10 animate-float"></div>
  <div class="absolute bottom-20 right-20 w-32 h-32 bg-gradient-to-br from-purple-400 to-pink-600 rounded-full opacity-10 animate-float-delayed"></div>
  <div class="absolute top-1/3 right-1/4 w-16 h-16 bg-gradient-to-br from-green-400 to-teal-600 rounded-full opacity-10 animate-float"></div>

  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="grid lg:grid-cols-2 gap-12 items-center">
      <!-- Left Content -->
      <div data-controller="fade-in" class="text-left space-y-8">
        <!-- Badge -->
        <div class="inline-flex items-center px-5 py-2 bg-red-50 rounded-full text-sm font-medium">
          <span class="w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse"></span>
          <span class="text-red-600">70% Off for first 3 months</span>
        </div>

        <!-- Main heading -->
        <h1 class="text-5xl md:text-6xl font-bold text-gray-900 leading-tight">
          Make more time for the
          <span class="relative">
            <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">work that matters</span>
            <svg class="absolute -bottom-2 left-0 w-full" height="8" viewBox="0 0 200 8" fill="none">
              <path d="M2 5.5C2 5.5 40 2 100 2C160 2 198 5.5 198 5.5" stroke="url(#gradient)" stroke-width="3" stroke-linecap="round"/>
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stop-color="#2563EB"/>
                  <stop offset="100%" stop-color="#4F46E5"/>
                </linearGradient>
              </defs>
            </svg>
          </span>
          most
        </h1>

        <!-- Subheading -->
        <p class="text-xl text-gray-600 leading-relaxed">
          Transform social conversations into qualified leads with AI-powered monitoring, 
          intelligent analysis, and automated engagement.
        </p>

        <!-- CTA Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 mt-8">
          <a href="<%= new_user_registration_path %>" 
             class="group inline-flex items-center justify-center px-8 py-4 text-white bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300">
            Get Started - It's Free
            <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
            </svg>
          </a>
          <a href="#demo" 
             class="group inline-flex items-center justify-center px-8 py-4 text-gray-700 bg-white border-2 border-gray-200 rounded-lg font-semibold hover:border-gray-300 hover:shadow-lg transform hover:-translate-y-1 transition-all duration-300">
            <svg class="w-5 h-5 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"/>
            </svg>
            Watch Demo
          </a>
        </div>

      </div>
      
      <!-- Right Content - Hero Image -->
      <div class="relative lg:block hidden">
        <div class="relative z-10">
          <!-- Dashboard Preview Card -->
          <div class="bg-white rounded-2xl shadow-2xl p-1 transform rotate-3 hover:rotate-0 transition-transform duration-500">
            <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-8">
              <!-- Mini Dashboard -->
              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <div class="h-3 bg-gray-300 rounded w-24"></div>
                  <div class="h-3 bg-blue-500 rounded w-16"></div>
                </div>
                <div class="grid grid-cols-3 gap-4">
                  <div class="bg-white p-4 rounded-lg">
                    <div class="h-2 bg-gray-200 rounded mb-2"></div>
                    <div class="h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded"></div>
                  </div>
                  <div class="bg-white p-4 rounded-lg">
                    <div class="h-2 bg-gray-200 rounded mb-2"></div>
                    <div class="h-8 bg-gradient-to-r from-green-500 to-teal-500 rounded"></div>
                  </div>
                  <div class="bg-white p-4 rounded-lg">
                    <div class="h-2 bg-gray-200 rounded mb-2"></div>
                    <div class="h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded"></div>
                  </div>
                </div>
                <div class="bg-white rounded-lg p-4">
                  <div class="space-y-2">
                    <div class="h-2 bg-gray-200 rounded w-full"></div>
                    <div class="h-2 bg-gray-200 rounded w-3/4"></div>
                    <div class="h-2 bg-gray-200 rounded w-5/6"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Floating Stats Cards -->
          <div class="absolute -top-4 -left-4 bg-white rounded-lg shadow-xl p-4 animate-float">
            <div class="flex items-center space-x-2">
              <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-green-600 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
                </svg>
              </div>
              <div>
                <div class="text-xs text-gray-500">Leads Generated</div>
                <div class="text-lg font-bold text-gray-900">2,547</div>
              </div>
            </div>
          </div>
          
          <div class="absolute -bottom-4 -right-4 bg-white rounded-lg shadow-xl p-4 animate-float-delayed">
            <div class="flex items-center space-x-2">
              <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"/>
                  <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z"/>
                </svg>
              </div>
              <div>
                <div class="text-xs text-gray-500">Mentions Today</div>
                <div class="text-lg font-bold text-gray-900">1,284</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Client Logos -->
    <div class="mt-20 pt-12 border-t border-gray-200">
      <div class="text-center mb-8">
        <p class="text-sm text-gray-500 uppercase tracking-wider">Trusted by leading companies</p>
      </div>
      <div class="flex flex-wrap justify-center items-center gap-8 opacity-60">
        <div class="text-2xl font-bold text-gray-400">TechCorp</div>
        <div class="text-2xl font-bold text-gray-400">StartupHub</div>
        <div class="text-2xl font-bold text-gray-400">GrowthCo</div>
        <div class="text-2xl font-bold text-gray-400">Innovate</div>
        <div class="text-2xl font-bold text-gray-400">NextGen</div>
      </div>
    </div>
  </div>
</section>

<!-- How It Works Section -->
<section class="py-20 bg-white" data-controller="scroll-reveal">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16" data-scroll-reveal-target="element" data-scroll-reveal-delay-value="0">
      <p class="text-sm text-blue-600 font-semibold uppercase tracking-wider mb-2">How It Works</p>
      <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
        Let's get started in <span class="text-blue-600">3 easy steps</span>
      </h2>
    </div>
    
    <!-- Steps Container -->
    <div class="relative">
      <div class="grid md:grid-cols-3 gap-8 relative">
        <!-- Step 1 -->
        <div class="relative" data-scroll-reveal-target="element" data-scroll-reveal-delay-value="100">
          <!-- Arrow connector (hidden on mobile) -->
          <svg class="hidden md:block absolute -right-4 top-16 w-24 h-12 text-gray-300" viewBox="0 0 100 50" fill="none">
            <path d="M 10 25 Q 50 10 90 25" stroke="currentColor" stroke-width="2" stroke-dasharray="5 5"/>
            <path d="M 85 20 L 90 25 L 85 30" stroke="currentColor" stroke-width="2" fill="none"/>
          </svg>
          
          <div class="text-center group">
            <!-- Number Badge -->
            <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
              <span class="text-3xl font-bold text-blue-600">1</span>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-3">Connect Your Keywords</h3>
            <p class="text-gray-600 leading-relaxed">
              Define the keywords, hashtags, and brand mentions you want to monitor across social platforms.
            </p>
          </div>
        </div>
        
        <!-- Step 2 -->
        <div class="relative" data-scroll-reveal-target="element" data-scroll-reveal-delay-value="200">
          <!-- Arrow connector (hidden on mobile) -->
          <svg class="hidden md:block absolute -right-4 top-16 w-24 h-12 text-gray-300" viewBox="0 0 100 50" fill="none">
            <path d="M 10 25 Q 50 40 90 25" stroke="currentColor" stroke-width="2" stroke-dasharray="5 5"/>
            <path d="M 85 20 L 90 25 L 85 30" stroke="currentColor" stroke-width="2" fill="none"/>
          </svg>
          
          <div class="text-center group">
            <!-- Number Badge -->
            <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-green-50 to-emerald-100 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
              <span class="text-3xl font-bold text-green-600">2</span>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-3">AI Analysis & Scoring</h3>
            <p class="text-gray-600 leading-relaxed">
              Our AI analyzes sentiment, intent, and context to identify and score high-value prospects automatically.
            </p>
          </div>
        </div>
        
        <!-- Step 3 -->
        <div class="relative" data-scroll-reveal-target="element" data-scroll-reveal-delay-value="300">
          <div class="text-center group">
            <!-- Number Badge -->
            <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-purple-50 to-pink-100 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
              <span class="text-3xl font-bold text-purple-600">3</span>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-3">Convert to Customers</h3>
            <p class="text-gray-600 leading-relaxed">
              Engage with qualified leads at the perfect moment and watch them convert into paying customers.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Services Section -->
<section class="py-20 bg-gray-50" data-controller="scroll-reveal">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16" data-scroll-reveal-target="element" data-scroll-reveal-delay-value="0">
      <p class="text-sm text-blue-600 font-semibold uppercase tracking-wider mb-2">Services</p>
      <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
        Best Features For <span class="text-blue-600">Effective Business</span>
      </h2>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <!-- Service 1 -->
      <div class="bg-white rounded-2xl p-8 hover:shadow-2xl transition-all duration-300 group" data-scroll-reveal-target="element" data-scroll-reveal-delay-value="100">
        <div class="relative mb-6">
          <div class="w-16 h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
            </svg>
          </div>
          <div class="absolute -top-2 -right-2 w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
            <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
          </div>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-3">Real-Time Monitoring</h3>
        <p class="text-gray-600 leading-relaxed">Monitor social media platforms 24/7 to catch every mention and conversation about your brand or keywords.</p>
        <a href="#" class="inline-flex items-center mt-4 text-blue-600 font-medium hover:text-blue-700 group">
          Learn more
          <svg class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
          </svg>
        </a>
      </div>

      <!-- Service 2 -->
      <div class="bg-white rounded-2xl p-8 hover:shadow-2xl transition-all duration-300 group" data-scroll-reveal-target="element" data-scroll-reveal-delay-value="200">
        <div class="relative mb-6">
          <div class="w-16 h-16 bg-gradient-to-br from-purple-100 to-purple-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
            </svg>
          </div>
          <div class="absolute -top-2 -right-2 w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center">
            <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
          </div>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-3">AI-Powered Analysis</h3>
        <p class="text-gray-600 leading-relaxed">Advanced AI analyzes sentiment, intent, and context to identify high-value prospects automatically.</p>
        <a href="#" class="inline-flex items-center mt-4 text-purple-600 font-medium hover:text-purple-700 group">
          Learn more
          <svg class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
          </svg>
        </a>
      </div>

      <!-- Service 3 -->
      <div class="bg-white rounded-2xl p-8 hover:shadow-2xl transition-all duration-300 group" data-scroll-reveal-target="element" data-scroll-reveal-delay-value="300">
        <div class="relative mb-6">
          <div class="w-16 h-16 bg-gradient-to-br from-green-100 to-green-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"/>
            </svg>
          </div>
          <div class="absolute -top-2 -right-2 w-6 h-6 bg-green-600 rounded-full flex items-center justify-center">
            <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
          </div>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-3">Lead Scoring</h3>
        <p class="text-gray-600 leading-relaxed">Automatically score and prioritize leads based on their likelihood to convert into customers.</p>
        <a href="#" class="inline-flex items-center mt-4 text-green-600 font-medium hover:text-green-700 group">
          Learn more
          <svg class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
          </svg>
        </a>
      </div>

      <!-- Service 4 -->
      <div class="bg-white rounded-2xl p-8 hover:shadow-2xl transition-all duration-300 group" data-scroll-reveal-target="element" data-scroll-reveal-delay-value="400">
        <div class="relative mb-6">
          <div class="w-16 h-16 bg-gradient-to-br from-yellow-100 to-yellow-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
            </svg>
          </div>
          <div class="absolute -top-2 -right-2 w-6 h-6 bg-yellow-600 rounded-full flex items-center justify-center">
            <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M11 3a1 1 0 10-2 0v1a1 1 0 102 0V3zM15.657 5.757a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707zM18 10a1 1 0 01-1 1h-1a1 1 0 110-2h1a1 1 0 011 1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zM5 10a1 1 0 01-1 1H3a1 1 0 110-2h1a1 1 0 011 1zM8 16v-1h4v1a2 2 0 11-4 0zM12 14c.015-.34.208-.646.477-.859a4 4 0 10-4.954 0c.27.213.462.519.476.859h4.002z"/>
            </svg>
          </div>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-3">Smart Engagement</h3>
        <p class="text-gray-600 leading-relaxed">Engage with prospects at the perfect moment with AI-generated personalized responses.</p>
        <a href="#" class="inline-flex items-center mt-4 text-yellow-600 font-medium hover:text-yellow-700 group">
          Learn more
          <svg class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
          </svg>
        </a>
      </div>

      <!-- Service 5 -->
      <div class="bg-white rounded-2xl p-8 hover:shadow-2xl transition-all duration-300 group" data-scroll-reveal-target="element" data-scroll-reveal-delay-value="500">
        <div class="relative mb-6">
          <div class="w-16 h-16 bg-gradient-to-br from-red-100 to-red-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
          </div>
          <div class="absolute -top-2 -right-2 w-6 h-6 bg-red-600 rounded-full flex items-center justify-center">
            <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd"/>
            </svg>
          </div>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-3">CRM Integration</h3>
        <p class="text-gray-600 leading-relaxed">Seamlessly integrate with your existing CRM and marketing tools for streamlined workflows.</p>
        <a href="#" class="inline-flex items-center mt-4 text-red-600 font-medium hover:text-red-700 group">
          Learn more
          <svg class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
          </svg>
        </a>
      </div>

      <!-- Service 6 -->
      <div class="bg-white rounded-2xl p-8 hover:shadow-2xl transition-all duration-300 group" data-scroll-reveal-target="element" data-scroll-reveal-delay-value="600">
        <div class="relative mb-6">
          <div class="w-16 h-16 bg-gradient-to-br from-indigo-100 to-indigo-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <div class="absolute -top-2 -right-2 w-6 h-6 bg-indigo-600 rounded-full flex items-center justify-center">
            <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
            </svg>
          </div>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-3">Analytics & Reporting</h3>
        <p class="text-gray-600 leading-relaxed">Comprehensive analytics dashboard to track performance and optimize your lead generation strategy.</p>
        <a href="#" class="inline-flex items-center mt-4 text-indigo-600 font-medium hover:text-indigo-700 group">
          Learn more
          <svg class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
          </svg>
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Statistics Section -->
<section class="py-20 bg-gradient-to-br from-blue-600 to-indigo-700">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">
        A focused platform with <span class="text-yellow-400">specialized capabilities</span>
      </h2>
      <p class="text-xl text-blue-100 max-w-3xl mx-auto">
        Join thousands of businesses that have transformed their lead generation with our AI-powered platform
      </p>
    </div>
    
    <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
      <!-- Stat 1 -->
      <div class="text-center" data-controller="counter">
        <div class="text-4xl md:text-5xl font-bold text-white mb-2">
          <span data-counter-target="number" data-counter-end-value="12547">0</span>+
        </div>
        <h4 class="text-lg font-semibold text-yellow-400 mb-1">Happy Clients</h4>
        <p class="text-blue-100 text-sm">Businesses thriving with our platform</p>
      </div>
      
      <!-- Stat 2 -->
      <div class="text-center" data-controller="counter">
        <div class="text-4xl md:text-5xl font-bold text-white mb-2">
          <span data-counter-target="number" data-counter-end-value="2">0</span>M+
        </div>
        <h4 class="text-lg font-semibold text-yellow-400 mb-1">Leads Generated</h4>
        <p class="text-blue-100 text-sm">Qualified leads delivered to clients</p>
      </div>
      
      <!-- Stat 3 -->
      <div class="text-center" data-controller="counter">
        <div class="text-4xl md:text-5xl font-bold text-white mb-2">
          <span data-counter-target="number" data-counter-end-value="500">0</span>M+
        </div>
        <h4 class="text-lg font-semibold text-yellow-400 mb-1">Mentions Analyzed</h4>
        <p class="text-blue-100 text-sm">Social conversations processed daily</p>
      </div>
      
      <!-- Stat 4 -->
      <div class="text-center" data-controller="counter">
        <div class="text-4xl md:text-5xl font-bold text-white mb-2">
          <span data-counter-target="number" data-counter-end-value="98">0</span>%
        </div>
        <h4 class="text-lg font-semibold text-yellow-400 mb-1">Satisfaction Rate</h4>
        <p class="text-blue-100 text-sm">Customer satisfaction score</p>
      </div>
    </div>
  </div>
</section>

<!-- Pricing Section -->
<section class="py-20 bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <p class="text-sm text-blue-600 font-semibold uppercase tracking-wider mb-2">Pricing</p>
      <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
        Best Pricing Package <span class="text-blue-600">Start Business</span>
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Choose the perfect plan that fits your business needs and budget
      </p>
    </div>

    <!-- Pricing Toggle -->
    <div class="flex justify-center mb-12" data-controller="pricing-toggle">
      <div class="bg-gray-100 p-1 rounded-full inline-flex">
        <button data-action="click->pricing-toggle#toggle" class="relative flex items-center">
          <span class="px-4 py-2 text-gray-700 font-medium">Monthly</span>
          <div class="mx-2 w-14 h-7 bg-indigo-600 rounded-full relative" data-pricing-toggle-target="toggle">
            <span class="absolute left-1 top-1 bg-white w-5 h-5 rounded-full shadow transition-transform duration-200"></span>
          </div>
          <span class="px-4 py-2 text-gray-700 font-medium">Annual</span>
          <span data-pricing-toggle-target="savings" class="ml-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full opacity-0 transition-opacity">Save 20%</span>
        </button>
      </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- Starter Plan -->
      <div class="relative bg-white rounded-2xl shadow-lg overflow-hidden transform hover:scale-105 transition-transform duration-300">
        <div class="p-8">
          <h3 class="text-2xl font-bold text-gray-900 mb-2">Starter</h3>
          <p class="text-gray-600 mb-6">Perfect for small businesses</p>
          <div class="mb-6">
            <span data-pricing-toggle-target="monthlyPrice" class="text-5xl font-bold text-gray-900">$49</span>
            <span data-pricing-toggle-target="annualPrice" class="text-5xl font-bold text-gray-900 hidden">$39</span>
            <span data-pricing-toggle-target="period" class="text-gray-600">/month</span>
          </div>
          <ul class="space-y-4 mb-8">
            <li class="flex items-start">
              <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
              </svg>
              <span class="text-gray-700">Up to 1,000 mentions/month</span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
              </svg>
              <span class="text-gray-700">5 keywords</span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
              </svg>
              <span class="text-gray-700">Basic analytics</span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
              </svg>
              <span class="text-gray-700">Email support</span>
            </li>
          </ul>
          <button data-action="click->pricing-toggle#selectPlan" data-plan="starter" class="w-full py-3 px-6 bg-gray-900 text-white rounded-lg font-semibold hover:bg-gray-800 transition-colors">
            Get Started
          </button>
        </div>
      </div>

      <!-- Professional Plan -->
      <div class="relative bg-gradient-to-b from-indigo-500 to-purple-600 rounded-2xl shadow-xl overflow-hidden transform hover:scale-105 transition-transform duration-300">
        <div class="absolute top-0 right-0 bg-yellow-400 text-gray-900 text-xs font-bold px-3 py-1 rounded-bl-lg">
          MOST POPULAR
        </div>
        <div class="p-8 text-white">
          <h3 class="text-2xl font-bold mb-2">Professional</h3>
          <p class="text-indigo-100 mb-6">For growing teams</p>
          <div class="mb-6">
            <span data-pricing-toggle-target="monthlyPrice" class="text-5xl font-bold">$149</span>
            <span data-pricing-toggle-target="annualPrice" class="text-5xl font-bold hidden">$119</span>
            <span data-pricing-toggle-target="period" class="text-indigo-100">/month</span>
          </div>
          <ul class="space-y-4 mb-8">
            <li class="flex items-start">
              <svg class="w-5 h-5 text-yellow-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
              </svg>
              <span>Up to 10,000 mentions/month</span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-yellow-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
              </svg>
              <span>25 keywords</span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-yellow-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
              </svg>
              <span>Advanced analytics & AI insights</span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-yellow-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
              </svg>
              <span>CRM integrations</span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-yellow-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
              </svg>
              <span>Priority support</span>
            </li>
          </ul>
          <button data-action="click->pricing-toggle#selectPlan" data-plan="professional" class="w-full py-3 px-6 bg-white text-indigo-600 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
            Get Started
          </button>
        </div>
      </div>

      <!-- Enterprise Plan -->
      <div class="relative bg-white rounded-2xl shadow-lg overflow-hidden transform hover:scale-105 transition-transform duration-300">
        <div class="p-8">
          <h3 class="text-2xl font-bold text-gray-900 mb-2">Enterprise</h3>
          <p class="text-gray-600 mb-6">For large organizations</p>
          <div class="mb-6">
            <span class="text-5xl font-bold text-gray-900">Custom</span>
          </div>
          <ul class="space-y-4 mb-8">
            <li class="flex items-start">
              <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
              </svg>
              <span class="text-gray-700">Unlimited mentions</span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
              </svg>
              <span class="text-gray-700">Unlimited keywords</span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
              </svg>
              <span class="text-gray-700">Custom AI training</span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
              </svg>
              <span class="text-gray-700">Dedicated account manager</span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
              </svg>
              <span class="text-gray-700">SLA & 24/7 support</span>
            </li>
          </ul>
          <button data-action="click->pricing-toggle#selectPlan" data-plan="enterprise" class="w-full py-3 px-6 bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-lg font-semibold hover:shadow-lg transition-all">
            Contact Sales
          </button>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Testimonials Section -->
<section class="py-20 bg-gray-50" data-controller="testimonial-carousel">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
        What Our Customers Say
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Join thousands of businesses that trust us with their lead generation
      </p>
    </div>

    <div class="relative max-w-4xl mx-auto">
      <!-- Testimonial Cards -->
      <div class="relative">
        <!-- Testimonial 1 -->
        <div data-testimonial-carousel-target="testimonial" class="bg-white rounded-2xl p-8 shadow-lg">
          <div class="flex mb-4">
            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
          </div>
          <p class="text-gray-700 mb-6 italic text-lg">"This platform transformed our lead generation process. We've seen a 300% increase in qualified leads within just 2 months!"</p>
          <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full mr-4"></div>
            <div>
              <div class="font-semibold text-gray-900">Sarah Johnson</div>
              <div class="text-gray-600 text-sm">Marketing Director, TechCorp</div>
            </div>
          </div>
        </div>

        <!-- Testimonial 2 -->
        <div data-testimonial-carousel-target="testimonial" class="bg-white rounded-2xl p-8 shadow-lg hidden">
          <div class="flex mb-4">
            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
          </div>
          <p class="text-gray-700 mb-6 italic text-lg">"The AI is incredibly accurate at identifying real opportunities. We've closed 5 major deals from leads this platform found."</p>
          <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-full mr-4"></div>
            <div>
              <div class="font-semibold text-gray-900">Michael Chen</div>
              <div class="text-gray-600 text-sm">CEO, Growth Startup</div>
            </div>
          </div>
        </div>

        <!-- Testimonial 3 -->
        <div data-testimonial-carousel-target="testimonial" class="bg-white rounded-2xl p-8 shadow-lg hidden">
          <div class="flex mb-4">
            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
          </div>
          <p class="text-gray-700 mb-6 italic text-lg">"Best investment we've made. ROI was positive within weeks. The automation saves us countless hours."</p>
          <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-r from-pink-500 to-red-500 rounded-full mr-4"></div>
            <div>
              <div class="font-semibold text-gray-900">Emily Rodriguez</div>
              <div class="text-gray-600 text-sm">Growth Manager, SaaS Pro</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <div class="flex justify-center mt-8 space-x-2">
        <button data-testimonial-carousel-target="indicator" data-action="click->testimonial-carousel#goToSlide" data-index="0" class="w-3 h-3 rounded-full bg-indigo-600"></button>
        <button data-testimonial-carousel-target="indicator" data-action="click->testimonial-carousel#goToSlide" data-index="1" class="w-3 h-3 rounded-full bg-gray-300"></button>
        <button data-testimonial-carousel-target="indicator" data-action="click->testimonial-carousel#goToSlide" data-index="2" class="w-3 h-3 rounded-full bg-gray-300"></button>
      </div>

      <!-- Arrow Navigation -->
      <button data-action="click->testimonial-carousel#previous" data-testimonial-carousel-target="prevButton" class="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-12 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-shadow">
        <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
        </svg>
      </button>
      <button data-action="click->testimonial-carousel#next" data-testimonial-carousel-target="nextButton" class="absolute right-0 top-1/2 -translate-y-1/2 translate-x-12 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-shadow">
        <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
        </svg>
      </button>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-20 bg-white" data-controller="accordion">
  <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
        Frequently Asked Questions
      </h2>
      <p class="text-xl text-gray-600">
        Everything you need to know about AI Lead Generation
      </p>
    </div>

    <div class="space-y-4">
      <!-- FAQ Item 1 -->
      <div class="bg-gray-50 rounded-lg shadow-md" data-accordion-target="item">
        <button class="w-full px-6 py-4 text-left flex items-center justify-between" data-action="click->accordion#toggle" data-accordion-target="question">
          <span class="text-lg font-semibold text-gray-900">How does the AI identify qualified leads?</span>
          <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" data-accordion-target="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
          </svg>
        </button>
        <div class="px-6 pb-4" data-accordion-target="content">
          <p class="text-gray-600">Our AI analyzes multiple factors including sentiment, context, buying intent signals, and engagement patterns. It uses advanced natural language processing to understand the nuances of conversations and identify prospects who are actively looking for solutions like yours.</p>
        </div>
      </div>

      <!-- FAQ Item 2 -->
      <div class="bg-gray-50 rounded-lg shadow-md" data-accordion-target="item">
        <button class="w-full px-6 py-4 text-left flex items-center justify-between" data-action="click->accordion#toggle" data-accordion-target="question">
          <span class="text-lg font-semibold text-gray-900">Which social platforms do you monitor?</span>
          <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" data-accordion-target="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
          </svg>
        </button>
        <div class="hidden px-6 pb-4" data-accordion-target="content">
          <p class="text-gray-600">We currently monitor Twitter, Reddit, LinkedIn, Facebook Groups, and industry-specific forums. We're constantly adding new platforms based on customer demand. You can also integrate custom data sources through our API.</p>
        </div>
      </div>

      <!-- FAQ Item 3 -->
      <div class="bg-gray-50 rounded-lg shadow-md" data-accordion-target="item">
        <button class="w-full px-6 py-4 text-left flex items-center justify-between" data-action="click->accordion#toggle" data-accordion-target="question">
          <span class="text-lg font-semibold text-gray-900">Can I integrate with my existing CRM?</span>
          <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" data-accordion-target="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
          </svg>
        </button>
        <div class="hidden px-6 pb-4" data-accordion-target="content">
          <p class="text-gray-600">Yes! We have native integrations with Salesforce, HubSpot, Pipedrive, and many other popular CRMs. We also offer a robust API and Zapier integration for custom connections to any tool in your stack.</p>
        </div>
      </div>

      <!-- FAQ Item 4 -->
      <div class="bg-gray-50 rounded-lg shadow-md" data-accordion-target="item">
        <button class="w-full px-6 py-4 text-left flex items-center justify-between" data-action="click->accordion#toggle" data-accordion-target="question">
          <span class="text-lg font-semibold text-gray-900">How quickly can I see results?</span>
          <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" data-accordion-target="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
          </svg>
        </button>
        <div class="hidden px-6 pb-4" data-accordion-target="content">
          <p class="text-gray-600">Most customers start seeing qualified leads within 24-48 hours of setup. The AI learns and improves over time, so lead quality typically increases significantly within the first 2-4 weeks as it adapts to your specific business needs.</p>
        </div>
      </div>

      <!-- FAQ Item 5 -->
      <div class="bg-gray-50 rounded-lg shadow-md" data-accordion-target="item">
        <button class="w-full px-6 py-4 text-left flex items-center justify-between" data-action="click->accordion#toggle" data-accordion-target="question">
          <span class="text-lg font-semibold text-gray-900">Do you offer a free trial?</span>
          <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" data-accordion-target="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
          </svg>
        </button>
        <div class="hidden px-6 pb-4" data-accordion-target="content">
          <p class="text-gray-600">Yes! We offer a 14-day free trial with full access to all features. No credit card required to start. You can explore the platform, set up your keywords, and start seeing real results before making any commitment.</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-gradient-to-r from-indigo-600 to-purple-700">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
      Ready to Transform Your Lead Generation?
    </h2>
    <p class="text-xl text-indigo-100 mb-8">
      Join thousands of businesses already using AI to find and convert high-quality leads.
    </p>
    <div class="flex flex-col sm:flex-row gap-4 justify-center">
      <a href="<%= new_user_registration_path %>" class="inline-flex items-center justify-center px-8 py-4 text-lg font-bold text-indigo-600 bg-white rounded-full hover:bg-gray-100 transition-colors">
        Start Your Free Trial
      </a>
      <a href="#" class="inline-flex items-center justify-center px-8 py-4 text-lg font-bold text-white border-2 border-white rounded-full hover:bg-white/10 transition-colors">
        Schedule a Demo
      </a>
    </div>
  </div>
</section>
