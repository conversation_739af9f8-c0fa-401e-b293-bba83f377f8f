<% content_for :title, "Lead Analytics Dashboard - AI Lead Generation" %>

<div class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-indigo-50">
  <!-- Header -->
  <div class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <!-- Breadcrumb -->
        <nav class="flex items-center space-x-4" aria-label="Breadcrumb">
          <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
              <%= link_to leads_path, class: "inline-flex items-center text-gray-500 hover:text-indigo-600 transition-colors duration-200" do %>
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 919.288 0M15 7a3 3 0 11-6 0 3 3 0 616 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                Leads
              <% end %>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="ml-1 text-gray-900 font-medium md:ml-2">Analytics</span>
              </div>
            </li>
          </ol>
        </nav>

        <!-- Actions -->
        <div class="flex items-center space-x-3">
          <%= link_to leads_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
            Back to Leads
          <% end %>
          
          <%= link_to export_leads_path(format: :csv), class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 shadow-sm hover:shadow-md" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Export Data
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Page Header -->
    <div class="text-center mb-8">
      <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full mb-4">
        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
      </div>
      <h1 class="text-3xl font-bold text-gray-900 mb-2">Lead Analytics Dashboard</h1>
      <p class="text-lg text-gray-600 max-w-2xl mx-auto">
        Comprehensive insights into your lead generation performance and conversion metrics
      </p>
    </div>

    <!-- Key Performance Indicators -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Total Leads -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-600 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 919.288 0M15 7a3 3 0 11-6 0 3 3 0 616 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <p class="text-sm font-medium text-gray-500">Total Leads</p>
            <p class="text-2xl font-bold text-gray-900"><%= @analytics[:total_leads] || 0 %></p>
            <% if (@analytics[:this_week] || 0) > 0 %>
              <p class="text-sm text-green-600">+<%= @analytics[:this_week] %> this week</p>
            <% else %>
              <p class="text-sm text-gray-500">No new leads this week</p>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Conversion Rate -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-green-600 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <p class="text-sm font-medium text-gray-500">Conversion Rate</p>
            <p class="text-2xl font-bold text-gray-900"><%= @analytics[:conversion_rate] || 0 %>%</p>
            <% if (@analytics[:converted_leads] || 0) > 0 %>
              <p class="text-sm text-green-600"><%= @analytics[:converted_leads] %> converted</p>
            <% else %>
              <p class="text-sm text-gray-500">No conversions yet</p>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Hot Leads -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-gradient-to-r from-red-400 to-red-600 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.879 16.121A3 3 0 1012.015 11L11 14H9c0 .768.293 1.536.879 2.121z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <p class="text-sm font-medium text-gray-500">Hot Leads</p>
            <p class="text-2xl font-bold text-gray-900"><%= @analytics[:hot_leads] || 0 %></p>
            <% if (@analytics[:hot_leads] || 0) > 0 %>
              <p class="text-sm text-red-600">High priority</p>
            <% else %>
              <p class="text-sm text-gray-500">No hot leads</p>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Follow-ups Needed -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <p class="text-sm font-medium text-gray-500">Follow-ups Needed</p>
            <p class="text-2xl font-bold text-gray-900"><%= @analytics[:needs_follow_up] || 0 %></p>
            <% if (@analytics[:needs_follow_up] || 0) > 0 %>
              <p class="text-sm text-yellow-600">Action required</p>
            <% else %>
              <p class="text-sm text-gray-500">All caught up</p>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
      <!-- Conversion Funnel -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50">
          <h3 class="text-lg font-semibold text-gray-900">Conversion Funnel</h3>
          <p class="text-sm text-gray-600 mt-1">Lead progression through your sales pipeline</p>
        </div>
        <div class="p-6">
          <% if @conversion_funnel.present? %>
            <div class="space-y-4">
              <% @conversion_funnel.each_with_index do |(stage, data), index| %>
                <div class="relative">
                  <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700 capitalize"><%= stage.humanize %></span>
                    <span class="text-sm text-gray-500"><%= data[:count] %> leads (<%= data[:percentage] %>%)</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-3">
                    <div class="bg-gradient-to-r from-indigo-400 to-purple-500 h-3 rounded-full transition-all duration-500" style="width: <%= data[:percentage] %>%"></div>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-8">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">No funnel data</h3>
              <p class="mt-1 text-sm text-gray-500">Start adding leads to see conversion funnel analytics.</p>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Lead Sources -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50">
          <h3 class="text-lg font-semibold text-gray-900">Lead Sources</h3>
          <p class="text-sm text-gray-600 mt-1">Where your leads are coming from</p>
        </div>
        <div class="p-6">
          <% if @analytics[:source_breakdown].present? %>
            <div class="space-y-4">
              <% (@analytics[:source_breakdown] || {}).each do |source, count| %>
                <% percentage = (@analytics[:total_leads] || 0) > 0 ? (count.to_f / (@analytics[:total_leads] || 1) * 100).round(1) : 0 %>
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div class="w-3 h-3 bg-gradient-to-r from-indigo-400 to-purple-500 rounded-full mr-3"></div>
                    <span class="text-sm font-medium text-gray-700 capitalize"><%= source.presence || "Direct" %></span>
                  </div>
                  <div class="text-right">
                    <span class="text-sm font-semibold text-gray-900"><%= count %></span>
                    <span class="text-xs text-gray-500 ml-1">(<%= percentage %>%)</span>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-8">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">No source data</h3>
              <p class="mt-1 text-sm text-gray-500">Lead source tracking will appear here.</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Performance Metrics -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-8">
      <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50">
        <h3 class="text-lg font-semibold text-gray-900">Performance Metrics</h3>
        <p class="text-sm text-gray-600 mt-1">Detailed analytics and trends</p>
      </div>
      <div class="p-6">
        <% if @performance_metrics.present? %>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Average Qualification Score -->
            <div class="text-center">
              <div class="text-3xl font-bold text-indigo-600 mb-2"><%= (@performance_metrics || {})[:avg_qualification_score] || 0 %></div>
              <div class="text-sm text-gray-500">Average Qualification Score</div>
              <div class="mt-2 w-full bg-gray-200 rounded-full h-2">
                <div class="bg-gradient-to-r from-indigo-400 to-purple-500 h-2 rounded-full" style="width: <%= ((@performance_metrics || {})[:avg_qualification_score] || 0) %>%"></div>
              </div>
            </div>

            <!-- Average Days to Convert -->
            <div class="text-center">
              <div class="text-3xl font-bold text-green-600 mb-2"><%= ((@performance_metrics || {})[:avg_days_to_convert] || 0) %></div>
              <div class="text-sm text-gray-500">Average Days to Convert</div>
              <div class="text-xs text-gray-400 mt-1">From first contact to conversion</div>
            </div>

            <!-- Lead Response Rate -->
            <div class="text-center">
              <div class="text-3xl font-bold text-purple-600 mb-2"><%= ((@performance_metrics || {})[:response_rate] || 0) %>%</div>
              <div class="text-sm text-gray-500">Lead Response Rate</div>
              <div class="text-xs text-gray-400 mt-1">Leads that responded to outreach</div>
            </div>
          </div>
        <% else %>
          <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No performance data</h3>
            <p class="mt-1 text-sm text-gray-500">Performance metrics will appear as you manage more leads.</p>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50">
        <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
        <p class="text-sm text-gray-600 mt-1">Latest lead interactions and updates</p>
      </div>
      <div class="p-6">
        <% if @analytics[:recent_activity].present? %>
          <div class="space-y-4">
            <% ((@analytics || {})[:recent_activity] || []).each do |activity| %>
              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-gradient-to-r from-indigo-400 to-purple-500 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                    </svg>
                  </div>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900"><%= activity[:description] %></p>
                  <p class="text-sm text-gray-500"><%= time_ago_in_words(activity[:timestamp]) %> ago</p>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No recent activity</h3>
            <p class="mt-1 text-sm text-gray-500">Lead activity will appear here as you manage your pipeline.</p>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>
