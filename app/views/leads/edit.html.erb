<% content_for :title, "Edit #{@lead.full_name_or_email} - AI Lead Generation" %>

<div class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-indigo-50">
  <!-- Header -->
  <div class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <!-- Breadcrumb -->
        <nav class="flex items-center space-x-4" aria-label="Breadcrumb">
          <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
              <%= link_to leads_path, class: "inline-flex items-center text-gray-500 hover:text-indigo-600 transition-colors duration-200" do %>
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                Leads
              <% end %>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
                <%= link_to @lead.full_name_or_email, lead_path(@lead), class: "ml-1 text-gray-500 hover:text-indigo-600 transition-colors duration-200 md:ml-2" %>
              </div>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="ml-1 text-gray-900 font-medium md:ml-2">Edit</span>
              </div>
            </li>
          </ol>
        </nav>

        <!-- Actions -->
        <div class="flex items-center space-x-3">
          <%= link_to lead_path(@lead), class: "inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
            View Lead
          <% end %>
          
          <%= link_to lead_path(@lead), method: :delete, class: "inline-flex items-center px-4 py-2 border border-red-300 text-sm font-medium rounded-lg text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200", data: { confirm: "Are you sure you want to delete this lead?" } do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            Delete
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Page Header -->
    <div class="text-center mb-8">
      <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full mb-4">
        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
        </svg>
      </div>
      <h1 class="text-3xl font-bold text-gray-900 mb-2">Edit Lead</h1>
      <p class="text-lg text-gray-600 max-w-2xl mx-auto">
        Update lead information and track their progress through your pipeline
      </p>
    </div>

    <!-- Form Container -->
    <div class="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden">
      <%= form_with(model: @lead, local: true, data: { controller: "lead-form" }, class: "lead-form") do |form| %>
        
        <!-- Error Messages -->
        <% if @lead.errors.any? %>
          <div class="bg-red-50 border-l-4 border-red-400 p-4 m-6 rounded-lg" role="alert">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  Please fix the following <%= pluralize(@lead.errors.count, "error") %>:
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc list-inside space-y-1">
                    <% @lead.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <div class="px-6 py-8 sm:px-8">
          <div class="space-y-8">
            <!-- Contact Information Section -->
            <div>
              <h2 class="text-xl font-bold text-gray-900 mb-6">Contact Information</h2>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Name -->
                <div>
                  <%= form.label :name, class: "block text-sm font-semibold text-gray-900 mb-2" do %>
                    Full Name <span class="text-red-500">*</span>
                  <% end %>
                  <%= form.text_field :name, 
                      class: "block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200",
                      placeholder: "John Doe",
                      required: true %>
                </div>

                <!-- Email -->
                <div>
                  <%= form.label :email, class: "block text-sm font-semibold text-gray-900 mb-2" do %>
                    Email Address <span class="text-red-500">*</span>
                  <% end %>
                  <%= form.email_field :email, 
                      class: "block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200",
                      placeholder: "<EMAIL>",
                      required: true %>
                </div>

                <!-- Phone -->
                <div>
                  <%= form.label :phone, class: "block text-sm font-semibold text-gray-900 mb-2" %>
                  <%= form.telephone_field :phone, 
                      class: "block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200",
                      placeholder: "+****************" %>
                </div>

                <!-- Company -->
                <div>
                  <%= form.label :company, class: "block text-sm font-semibold text-gray-900 mb-2" %>
                  <%= form.text_field :company, 
                      class: "block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200",
                      placeholder: "Acme Corporation" %>
                </div>

                <!-- Position -->
                <div class="md:col-span-2">
                  <%= form.label :position, class: "block text-sm font-semibold text-gray-900 mb-2" %>
                  <%= form.text_field :position, 
                      class: "block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200",
                      placeholder: "Senior Software Engineer" %>
                </div>
              </div>
            </div>

            <!-- Lead Classification Section -->
            <div class="pt-8 border-t border-gray-200">
              <h2 class="text-xl font-bold text-gray-900 mb-6">Lead Classification</h2>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Status -->
                <div>
                  <%= form.label :status, class: "block text-sm font-semibold text-gray-900 mb-3" %>
                  <div class="space-y-2">
                    <% %w[new contacted qualified converted rejected archived].each do |status| %>
                      <label class="relative flex cursor-pointer rounded-lg border border-gray-300 bg-white p-3 shadow-sm focus-within:ring-2 focus-within:ring-indigo-500 hover:border-indigo-300 transition-all duration-200 <%= 'border-indigo-500 bg-indigo-50' if @lead.status == status %>">
                        <%= form.radio_button :status, status, class: "sr-only" %>
                        <span class="flex flex-1 items-center">
                          <span class="block text-sm font-medium text-gray-900"><%= status.humanize %></span>
                        </span>
                        <svg class="h-5 w-5 text-indigo-600 <%= 'opacity-100' if @lead.status == status %> <%= 'opacity-0' unless @lead.status == status %> transition-opacity duration-200" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                      </label>
                    <% end %>
                  </div>
                </div>

                <!-- Priority -->
                <div>
                  <%= form.label :priority, class: "block text-sm font-semibold text-gray-900 mb-3" %>
                  <div class="space-y-2">
                    <% %w[low medium high urgent].each do |priority| %>
                      <label class="relative flex cursor-pointer rounded-lg border border-gray-300 bg-white p-3 shadow-sm focus-within:ring-2 focus-within:ring-indigo-500 hover:border-indigo-300 transition-all duration-200 <%= 'border-indigo-500 bg-indigo-50' if @lead.priority == priority %>">
                        <%= form.radio_button :priority, priority, class: "sr-only" %>
                        <span class="flex flex-1 items-center">
                          <span class="block text-sm font-medium text-gray-900"><%= priority.humanize %></span>
                        </span>
                        <svg class="h-5 w-5 text-indigo-600 <%= 'opacity-100' if @lead.priority == priority %> <%= 'opacity-0' unless @lead.priority == priority %> transition-opacity duration-200" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                      </label>
                    <% end %>
                  </div>
                </div>

                <!-- Temperature -->
                <div>
                  <%= form.label :temperature, class: "block text-sm font-semibold text-gray-900 mb-3" %>
                  <div class="space-y-2">
                    <% %w[cold warm hot].each do |temp| %>
                      <label class="relative flex cursor-pointer rounded-lg border border-gray-300 bg-white p-3 shadow-sm focus-within:ring-2 focus-within:ring-indigo-500 hover:border-indigo-300 transition-all duration-200 <%= 'border-indigo-500 bg-indigo-50' if @lead.temperature == temp %>">
                        <%= form.radio_button :temperature, temp, class: "sr-only" %>
                        <span class="flex flex-1 items-center">
                          <span class="block text-sm font-medium text-gray-900"><%= temp.humanize %></span>
                        </span>
                        <svg class="h-5 w-5 text-indigo-600 <%= 'opacity-100' if @lead.temperature == temp %> <%= 'opacity-0' unless @lead.temperature == temp %> transition-opacity duration-200" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                      </label>
                    <% end %>
                  </div>
                </div>

                <!-- Lead Stage -->
                <div>
                  <%= form.label :lead_stage, class: "block text-sm font-semibold text-gray-900 mb-3" %>
                  <div class="space-y-2">
                    <% %w[prospect qualified opportunity proposal negotiation closed].each do |stage| %>
                      <label class="relative flex cursor-pointer rounded-lg border border-gray-300 bg-white p-3 shadow-sm focus-within:ring-2 focus-within:ring-indigo-500 hover:border-indigo-300 transition-all duration-200 <%= 'border-indigo-500 bg-indigo-50' if @lead.lead_stage == stage %>">
                        <%= form.radio_button :lead_stage, stage, class: "sr-only" %>
                        <span class="flex flex-1 items-center">
                          <span class="block text-sm font-medium text-gray-900"><%= stage.humanize %></span>
                        </span>
                        <svg class="h-5 w-5 text-indigo-600 <%= 'opacity-100' if @lead.lead_stage == stage %> <%= 'opacity-0' unless @lead.lead_stage == stage %> transition-opacity duration-200" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                      </label>
                    <% end %>
                  </div>
                </div>
              </div>
            </div>

            <!-- Additional Information Section -->
            <div class="pt-8 border-t border-gray-200">
              <h2 class="text-xl font-bold text-gray-900 mb-6">Additional Information</h2>
              <div class="space-y-6">
                <!-- Notes -->
                <div>
                  <%= form.label :notes, class: "block text-sm font-semibold text-gray-900 mb-2" %>
                  <%= form.text_area :notes, 
                      class: "block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200",
                      placeholder: "Add any relevant notes about this lead...",
                      rows: 4 %>
                </div>

                <!-- Follow-up and Qualification -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <%= form.label :next_follow_up, "Next Follow-up", class: "block text-sm font-semibold text-gray-900 mb-2" %>
                    <%= form.datetime_local_field :next_follow_up, 
                        class: "block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200" %>
                  </div>

                  <div>
                    <%= form.label :qualification_score, class: "block text-sm font-semibold text-gray-900 mb-2" %>
                    <%= form.number_field :qualification_score, 
                        class: "block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200",
                        placeholder: "0-100",
                        min: 0,
                        max: 100 %>
                  </div>

                  <div>
                    <%= form.label :conversion_value, "Conversion Value ($)", class: "block text-sm font-semibold text-gray-900 mb-2" %>
                    <%= form.number_field :conversion_value, 
                        class: "block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200",
                        placeholder: "0.00",
                        step: 0.01,
                        min: 0 %>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="px-6 py-6 bg-gray-50 border-t border-gray-200 sm:px-8">
          <div class="flex items-center justify-between">
            <%= link_to lead_path(@lead), class: "inline-flex items-center px-6 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
              Cancel
            <% end %>

            <%= form.submit "Update Lead", 
                class: "inline-flex items-center px-8 py-3 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105" %>
          </div>
        </div>

      <% end %>
    </div>
  </div>
</div>
