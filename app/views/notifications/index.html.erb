<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <h2 class="text-lg font-medium text-gray-900">Notifications</h2>
        
        <% if @unread_notifications.any? %>
          <%= button_to "Mark all as read", 
              mark_all_as_read_notifications_path,
              method: :patch,
              class: "inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",
              data: { turbo_method: :patch } %>
        <% end %>
      </div>
    </div>
    
    <%= turbo_stream_from current_user, :notifications %>
    
    <div class="divide-y divide-gray-200">
      <% if @unread_notifications.any? %>
        <div class="px-4 py-3 bg-gray-50">
          <h3 class="text-sm font-medium text-gray-900">Unread</h3>
        </div>
        <%= render "notifications/list", notifications: @unread_notifications %>
      <% end %>
      
      <% if @read_notifications.any? %>
        <div class="px-4 py-3 bg-gray-50">
          <h3 class="text-sm font-medium text-gray-900">Read</h3>
        </div>
        <%= render "notifications/list", notifications: @read_notifications %>
      <% end %>
      
      <% if @notifications.empty? %>
        <div class="p-8 text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
          </svg>
          <p class="text-gray-500">No notifications yet</p>
          <p class="text-sm text-gray-400 mt-1">We'll notify you when something important happens</p>
        </div>
      <% end %>
    </div>
  </div>
</div>