<div id="<%= dom_id(notification) %>" 
     class="<%= notification.unread? ? 'bg-blue-50' : 'bg-white' %> border-b border-gray-200 p-4 hover:bg-gray-50 transition-colors">
  <div class="flex items-start justify-between">
    <div class="flex-1">
      <div class="flex items-center gap-2">
        <% if notification.unread? %>
          <span class="inline-flex h-2 w-2 rounded-full bg-blue-600"></span>
        <% end %>
        <p class="text-sm font-medium text-gray-900">
          <%= notification.message %>
        </p>
      </div>
      <p class="mt-1 text-xs text-gray-500">
        <%= time_ago_in_words(notification.created_at) %> ago
      </p>
    </div>
    
    <div class="flex items-center gap-2 ml-4">
      <% if notification.url.present? && notification.url != "#" %>
        <%= link_to "View", notification.url, 
            class: "text-sm text-blue-600 hover:text-blue-800 font-medium",
            data: { turbo_frame: "_top" } %>
      <% end %>
      
      <% if notification.unread? %>
        <%= button_to mark_as_read_notification_path(notification), 
            method: :patch,
            class: "text-sm text-gray-600 hover:text-gray-800",
            data: { turbo_method: :patch } do %>
          Mark as read
        <% end %>
      <% end %>
    </div>
  </div>
</div>