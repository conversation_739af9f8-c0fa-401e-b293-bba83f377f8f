<div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Page Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">Profile Settings</h1>
      <p class="mt-2 text-sm text-gray-600">Manage your personal information and customize your profile</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Left Sidebar - Profile Photo and Quick Stats -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div class="p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Profile Photo</h2>
            
            <!-- Avatar Upload Section -->
            <div class="flex flex-col items-center">
              <div class="relative group">
                <div class="w-32 h-32 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white text-4xl font-bold shadow-lg">
                  <%= @user.email[0].upcase %>
                </div>
                <button class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                </button>
              </div>
              <button class="mt-4 px-4 py-2 text-sm font-medium text-indigo-600 bg-indigo-50 rounded-lg hover:bg-indigo-100 transition-colors duration-200">
                Change Photo
              </button>
            </div>

            <!-- Quick Stats -->
            <div class="mt-8 space-y-4">
              <div class="pb-4 border-b border-gray-200">
                <h3 class="text-sm font-medium text-gray-500 mb-3">Account Stats</h3>
                <div class="space-y-3">
                  <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Member Since</span>
                    <span class="text-sm font-medium text-gray-900"><%= @user.created_at.strftime('%b %Y') %></span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Keywords Tracked</span>
                    <span class="text-sm font-medium text-gray-900">12</span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Leads Generated</span>
                    <span class="text-sm font-medium text-gray-900">48</span>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 class="text-sm font-medium text-gray-500 mb-3">Account Type</h3>
                <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                  <span class="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                  Professional Plan
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Side - Profile Form -->
      <div class="lg:col-span-2">
        <%= form_with model: @user, url: update_profile_path, local: true, class: "space-y-6" do |form| %>
          <!-- Personal Information Section -->
          <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <h2 class="text-lg font-semibold text-gray-900">Personal Information</h2>
            </div>
            <div class="p-6 space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <%= form.label :first_name, class: "block text-sm font-medium text-gray-700 mb-2" %>
                  <%= form.text_field :first_name, 
                      class: "w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200",
                      placeholder: "Enter your first name" %>
                </div>
                <div>
                  <%= form.label :last_name, class: "block text-sm font-medium text-gray-700 mb-2" %>
                  <%= form.text_field :last_name, 
                      class: "w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200",
                      placeholder: "Enter your last name" %>
                </div>
              </div>

              <div>
                <%= form.label :email, class: "block text-sm font-medium text-gray-700 mb-2" %>
                <div class="relative">
                  <%= form.email_field :email, 
                      class: "w-full px-4 py-2.5 pr-10 border border-gray-300 rounded-lg bg-gray-50 cursor-not-allowed",
                      disabled: true %>
                  <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                  </div>
                </div>
                <p class="mt-1 text-xs text-gray-500">Your email cannot be changed for security reasons</p>
              </div>

              <div>
                <%= form.label :phone, class: "block text-sm font-medium text-gray-700 mb-2" %>
                <%= form.telephone_field :phone, 
                    class: "w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200",
                    placeholder: "+****************" %>
              </div>

              <div>
                <%= form.label :bio, "About Me", class: "block text-sm font-medium text-gray-700 mb-2" %>
                <%= form.text_area :bio, 
                    rows: 4,
                    class: "w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 resize-none",
                    placeholder: "Tell us a little about yourself..." %>
                <p class="mt-1 text-xs text-gray-500">Brief description for your profile. Max 500 characters.</p>
              </div>
            </div>
          </div>

          <!-- Professional Information Section -->
          <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <h2 class="text-lg font-semibold text-gray-900">Professional Information</h2>
            </div>
            <div class="p-6 space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <%= form.label :company, class: "block text-sm font-medium text-gray-700 mb-2" %>
                  <%= form.text_field :company, 
                      class: "w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200",
                      placeholder: "Your company name" %>
                </div>
                <div>
                  <%= form.label :job_title, class: "block text-sm font-medium text-gray-700 mb-2" %>
                  <%= form.text_field :job_title, 
                      class: "w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200",
                      placeholder: "Your job title" %>
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">LinkedIn Profile</label>
                <div class="flex">
                  <span class="inline-flex items-center px-3 rounded-l-lg border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                    linkedin.com/in/
                  </span>
                  <input type="text" 
                         class="flex-1 px-4 py-2.5 border border-gray-300 rounded-r-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                         placeholder="your-profile">
                </div>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="flex items-center justify-between">
            <button type="button" class="px-6 py-2.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
              Cancel
            </button>
            <div class="flex items-center space-x-3">
              <button type="button" class="px-6 py-2.5 text-sm font-medium text-indigo-600 bg-indigo-50 rounded-lg hover:bg-indigo-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                Save as Draft
              </button>
              <%= form.submit "Save Changes", 
                  class: "px-6 py-2.5 text-sm font-medium text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200 cursor-pointer" %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>