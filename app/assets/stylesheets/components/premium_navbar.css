/* Premium Navbar Styles */

/* Core navbar styling */
.premium-navbar {
  @apply sticky top-0 z-50 backdrop-blur-xl bg-white/95 border-b border-gray-200/20 shadow-sm transition-all duration-300;
  backdrop-filter: blur(20px) saturate(180%);
}

.premium-navbar:hover {
  @apply shadow-md;
  backdrop-filter: blur(20px) saturate(200%);
}

/* Premium container */
.premium-navbar-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* Premium navbar content */
.premium-navbar-content {
  @apply flex justify-between items-center h-16 lg:h-20;
}

/* Logo enhancement */
.premium-logo-container {
  @apply flex items-center space-x-3 transition-all duration-300 hover:scale-105;
}

.premium-logo-icon {
  @apply h-10 w-10 text-indigo-600 transition-all duration-300 drop-shadow-sm;
}

.premium-logo-icon:hover {
  @apply text-indigo-700;
  filter: drop-shadow(0 4px 6px rgba(99, 102, 241, 0.25));
}

.premium-logo-text {
  @apply text-2xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-800 bg-clip-text text-transparent transition-all duration-300;
}

.premium-logo-text:hover {
  @apply from-indigo-700 via-purple-700 to-indigo-900;
}

/* Premium search bar */
.premium-search-container {
  @apply hidden lg:block flex-1 max-w-lg mx-8;
}

.premium-search-form {
  @apply relative;
}

.premium-search-input {
  @apply w-full pl-12 pr-4 py-3 bg-gray-50/80 border border-gray-200/60 rounded-2xl text-sm transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-300 focus:bg-white;
  @apply placeholder:text-gray-400 backdrop-blur-sm;
}

.premium-search-input:hover {
  @apply bg-white border-gray-300/80;
}

.premium-search-icon {
  @apply absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 transition-colors duration-200;
}

.premium-search-input:focus ~ .premium-search-icon {
  @apply text-indigo-500;
}

/* Premium action buttons */
.premium-actions {
  @apply flex items-center space-x-3;
}

.premium-icon-button {
  @apply relative p-2.5 rounded-xl text-gray-600 hover:text-indigo-600 hover:bg-indigo-50/80 transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-indigo-500/20 focus:bg-indigo-50/80;
}

.premium-icon-button:hover {
  @apply shadow-sm transform translate-y-[-1px];
}

.premium-icon-button svg {
  @apply h-6 w-6 transition-transform duration-200;
}

.premium-icon-button:hover svg {
  @apply scale-110;
}

/* Notification badge */
.notification-badge {
  @apply absolute -top-1 -right-1 h-5 w-5 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs rounded-full;
  @apply flex items-center justify-center font-semibold shadow-sm;
  animation: pulse-notification 2s infinite;
}

@keyframes pulse-notification {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.9;
  }
}

/* Premium dropdown */
.premium-dropdown {
  @apply absolute right-0 z-50 mt-3 w-80 origin-top-right rounded-2xl;
  @apply bg-white/95 backdrop-blur-xl border border-gray-200/20 shadow-2xl;
  @apply focus:outline-none transform transition-all duration-200;
  backdrop-filter: blur(20px) saturate(180%);
}

.premium-dropdown.notifications-panel {
  @apply w-96;
}

.premium-dropdown.user-panel {
  @apply w-80;
}

.premium-dropdown-content {
  @apply py-2;
}

/* Dropdown header */
.premium-dropdown-header {
  @apply px-5 py-4 border-b border-gray-100/50;
}

.premium-dropdown-title {
  @apply text-lg font-semibold text-gray-900 mb-1;
}

.premium-dropdown-subtitle {
  @apply text-sm text-gray-500;
}

/* Dropdown items */
.premium-dropdown-item {
  @apply flex items-center px-5 py-3.5 text-sm text-gray-700 transition-all duration-150;
  @apply hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700;
  @apply focus:bg-gradient-to-r focus:from-indigo-50 focus:to-purple-50 focus:text-indigo-700 focus:outline-none;
}

.premium-dropdown-item:first-child {
  @apply rounded-t-xl;
}

.premium-dropdown-item:last-child {
  @apply rounded-b-xl;
}

.premium-dropdown-icon {
  @apply mr-4 h-5 w-5 text-gray-400 transition-colors duration-150;
  stroke-width: 1.5;
}

.premium-dropdown-item:hover .premium-dropdown-icon,
.premium-dropdown-item:focus .premium-dropdown-icon {
  @apply text-indigo-600;
}

.premium-dropdown-text {
  @apply flex-1;
}

.premium-dropdown-badge {
  @apply ml-3 px-2.5 py-1 bg-indigo-100 text-indigo-700 text-xs font-semibold rounded-full;
}

/* Dropdown divider */
.premium-dropdown-divider {
  @apply my-2 h-px bg-gradient-to-r from-transparent via-gray-200/60 to-transparent;
}

/* Premium avatar */
.premium-avatar {
  @apply h-10 w-10 rounded-2xl bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500;
  @apply flex items-center justify-center text-white font-semibold text-sm;
  @apply ring-2 ring-white shadow-lg transition-all duration-200;
}

.premium-avatar:hover {
  @apply ring-4 ring-indigo-100 shadow-xl transform scale-105;
}

.premium-avatar-large {
  @apply h-12 w-12 rounded-2xl bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500;
  @apply flex items-center justify-center text-white font-bold text-base;
  @apply ring-2 ring-white shadow-lg;
}

.premium-avatar-mobile {
  @apply h-12 w-12 rounded-2xl bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500;
  @apply flex items-center justify-center text-white font-bold text-base;
  @apply ring-2 ring-white shadow-lg;
}

.premium-avatar-ring {
  @apply absolute inset-0 rounded-2xl ring-2 ring-offset-2 ring-indigo-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300;
}

.premium-avatar-image {
  @apply h-10 w-10 rounded-2xl object-cover ring-2 ring-white shadow-lg transition-all duration-200;
}

.premium-avatar-image:hover {
  @apply ring-4 ring-indigo-100 shadow-xl transform scale-105;
}

/* Mobile menu button */
.premium-mobile-menu-button {
  @apply lg:hidden p-2.5 rounded-xl text-gray-600 hover:text-indigo-600 hover:bg-indigo-50/80;
  @apply focus:outline-none focus:ring-2 focus:ring-indigo-500/20 focus:bg-indigo-50/80;
  @apply transition-all duration-200;
}

.premium-mobile-menu-button:hover {
  @apply shadow-sm transform translate-y-[-1px];
}

.premium-mobile-menu-button svg {
  @apply h-6 w-6 transition-transform duration-200;
}

.premium-mobile-menu-button:hover svg {
  @apply scale-110;
}

/* Mobile menu */
.premium-mobile-menu {
  @apply lg:hidden absolute top-full left-0 right-0 z-40 mt-1 mx-4;
  @apply bg-white/95 backdrop-blur-xl rounded-2xl border border-gray-200/20 shadow-2xl;
  backdrop-filter: blur(20px) saturate(180%);
}

.premium-mobile-menu-content {
  @apply p-4 space-y-3;
}

/* Mobile search */
.premium-mobile-search {
  @apply relative mb-4;
}

.premium-mobile-search-input {
  @apply w-full pl-12 pr-4 py-3.5 bg-gray-50/80 border border-gray-200/60 rounded-2xl text-sm;
  @apply focus:outline-none focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-300 focus:bg-white;
  @apply placeholder:text-gray-400 backdrop-blur-sm transition-all duration-200;
}

.premium-mobile-search-icon {
  @apply absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400;
}

/* Mobile navigation links */
.premium-mobile-nav-item {
  @apply flex items-center px-4 py-3.5 text-gray-700 rounded-xl transition-all duration-150;
  @apply hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700;
}

.premium-mobile-nav-icon {
  @apply mr-4 h-5 w-5 text-gray-400 transition-colors duration-150;
  stroke-width: 1.5;
}

.premium-mobile-nav-item:hover .premium-mobile-nav-icon {
  @apply text-indigo-600;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .premium-navbar-content {
    @apply h-16;
  }
  
  .premium-logo-icon {
    @apply h-8 w-8;
  }
  
  .premium-logo-text {
    @apply text-xl;
  }
}

@media (max-width: 640px) {
  .premium-navbar-container {
    @apply px-3;
  }
  
  .premium-actions {
    @apply space-x-2;
  }
  
  .premium-icon-button {
    @apply p-2;
  }
  
  .premium-icon-button svg {
    @apply h-5 w-5;
  }
  
  .premium-avatar,
  .premium-avatar-image {
    @apply h-8 w-8;
  }
  
  .premium-dropdown {
    @apply w-72 right-2;
  }
}

/* Animation classes */
.slide-in-down {
  animation: slideInDown 0.3s ease-out forwards;
}

.slide-out-up {
  animation: slideOutUp 0.2s ease-in forwards;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideOutUp {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
}

/* Focus states for accessibility */
.premium-navbar *:focus {
  @apply outline-none;
}

.premium-navbar *:focus-visible {
  @apply ring-2 ring-indigo-500/20 ring-offset-2;
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  .premium-navbar {
    @apply bg-gray-900/95 border-gray-700/20;
  }
  
  .premium-dropdown {
    @apply bg-gray-800/95 border-gray-700/20;
  }
  
  .premium-dropdown-item {
    @apply text-gray-300 hover:text-white;
  }
  
  .premium-search-input,
  .premium-mobile-search-input {
    @apply bg-gray-800/80 border-gray-700/60 text-gray-200;
    @apply focus:border-indigo-400 focus:bg-gray-800;
  }
}

/* Premium User Button */
.premium-user-button {
  @apply relative flex items-center p-2 rounded-2xl transition-all duration-200;
  @apply hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500/20;
}

.premium-user-button:hover {
  @apply shadow-md;
}

/* Premium Badge */
.premium-badge {
  @apply inline-flex items-center px-2.5 py-1 bg-gradient-to-r from-indigo-500 to-purple-500 text-white text-xs font-semibold rounded-full;
}

.premium-badge-small {
  @apply inline-flex items-center px-2 py-0.5 bg-gradient-to-r from-green-400 to-emerald-500 text-white text-xs font-semibold rounded-full;
}

/* Notification Items */
.premium-notification-item {
  @apply px-5 py-4 hover:bg-gray-50/50 transition-colors duration-150 cursor-pointer;
}

.premium-notification-item:not(:last-child) {
  @apply border-b border-gray-100/50;
}

.premium-notification-icon {
  @apply h-8 w-8 rounded-xl flex items-center justify-center shadow-sm;
}

/* Dropdown Footer */
.premium-dropdown-footer {
  @apply px-5 py-3 border-t border-gray-100/50 bg-gray-50/30;
}

.premium-dropdown-action {
  @apply flex items-center justify-center text-sm font-semibold text-indigo-600 hover:text-indigo-700 transition-colors duration-150;
}

/* Premium Navigation Links */
.premium-nav-link {
  @apply relative px-4 py-3 rounded-xl font-medium text-gray-700 hover:text-indigo-700 transition-all duration-300;
}

/* Premium Buttons */
.premium-link-button {
  @apply px-4 py-2 text-sm font-semibold text-gray-700 hover:text-indigo-600 transition-colors duration-200;
}

.premium-cta-button {
  @apply inline-flex items-center px-5 py-2.5 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 group;
}

/* Mobile Specific */
.premium-mobile-button {
  @apply p-2 rounded-xl text-gray-600 hover:text-indigo-600 hover:bg-indigo-50/80 transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-indigo-500/20;
}

.mobile-hamburger {
  @apply flex flex-col justify-center items-center w-6 h-6;
}

.mobile-hamburger span {
  @apply block w-5 h-0.5 bg-current transition-all duration-300;
}

.mobile-hamburger span:nth-child(2) {
  @apply my-1;
}

.mobile-menu-panel {
  @apply absolute top-full left-0 right-0 z-40 bg-white border-t border-gray-200/20 shadow-2xl;
}

.premium-mobile-content {
  @apply max-h-[90vh] overflow-y-auto;
}

.premium-mobile-nav-item {
  @apply flex items-center justify-between px-4 py-3 rounded-xl text-gray-700 hover:bg-indigo-50/50 hover:text-indigo-700 transition-all duration-150 group;
}

.logout-item {
  @apply hover:bg-red-50 hover:text-red-700;
}

.logout-item .dropdown-item-icon {
  @apply text-red-400;
}

.logout-item:hover .dropdown-item-icon {
  @apply text-red-600;
}

/* Enhanced micro-interactions */
.premium-icon-button {
  position: relative;
  overflow: hidden;
}

.premium-icon-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.1) 0%, transparent 70%);
  transition: all 0.3s ease;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.premium-icon-button:hover::before {
  width: 100px;
  height: 100px;
}

/* Premium effects */
.premium-glow {
  position: relative;
}

.premium-glow::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(99, 102, 241, 0.03), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  border-radius: inherit;
}

.premium-glow:hover::after {
  opacity: 1;
}