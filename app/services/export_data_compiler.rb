# app/services/export_data_compiler.rb
class ExportDataCompiler
  def initialize(user:, date_range:, export_type: "overview")
    @user = user
    @date_range = date_range
    @export_type = export_type
  end

  def call
    exporter_class.new(user: @user, date_range: @date_range).export
  end

  private

  def exporter_class
    case @export_type
    when "overview"
      OverviewExporter
    when "keywords"
      KeywordsExporter
    when "leads"
      LeadsExporter
    when "performance"
      PerformanceExporter
    else
      DefaultExporter
    end
  end
end