# app/services/analytics_cache_service.rb
class AnalyticsCacheService
  attr_reader :user, :start_date, :end_date

  def initialize(user:, start_date:, end_date:)
    @user = user
    @start_date = start_date
    @end_date = end_date
  end

  def overview_metrics
    Rails.cache.fetch(cache_key("overview_metrics"), expires_in: 5.minutes) do
      calculate_overview_metrics
    end
  end

  def performance_data
    Rails.cache.fetch(cache_key("performance_data"), expires_in: 5.minutes) do
      fetch_performance_data
    end
  end

  def conversion_funnel
    Rails.cache.fetch(cache_key("conversion_funnel"), expires_in: 5.minutes) do
      calculate_conversion_funnel
    end
  end

  def top_performers
    Rails.cache.fetch(cache_key("top_performers"), expires_in: 5.minutes) do
      fetch_top_performers
    end
  rescue => e
    Rails.logger.error "Error fetching top performers: #{e.message}"
    default_top_performers
  end

  def clear_cache
    Rails.cache.delete(cache_key("overview_metrics"))
    Rails.cache.delete(cache_key("performance_data"))
    Rails.cache.delete(cache_key("conversion_funnel"))
    Rails.cache.delete(cache_key("top_performers"))
  end

  def preload_cache
    # Preload all cached data in parallel
    threads = []
    threads << Thread.new { overview_metrics }
    threads << Thread.new { performance_data }
    threads << Thread.new { conversion_funnel }
    threads << Thread.new { top_performers }
    threads.each(&:join)
  end

  private

  def cache_key(metric)
    "analytics:#{user.id}:#{metric}:#{start_date}:#{end_date}"
  end

  def calculate_overview_metrics
    {
      total_mentions: user.mentions.where(created_at: date_range).count,
      total_leads: user.leads.where(created_at: date_range).count,
      total_keywords: user.keywords.active.count,
      conversion_rate: calculate_conversion_rate,
      average_score: calculate_average_score,
      active_integrations: user.integrations.active.count
    }
  end

  def fetch_performance_data
    {
      mentions_trend: calculate_trend(:mentions),
      leads_trend: calculate_trend(:leads),
      conversion_trend: calculate_trend(:conversion_rate),
      response_time_avg: calculate_avg_response_time,
      engagement_rate: calculate_engagement_rate
    }
  end

  def calculate_conversion_funnel
    total_mentions = user.mentions.where(created_at: date_range).count
    analyzed_mentions = user.mentions.joins(:analysis_result).where(created_at: date_range).count
    qualified_leads = user.leads.where(created_at: date_range).count
    contacted_leads = user.leads.where(created_at: date_range, status: ["contacted", "converted"]).count

    {
      stages: [
        { name: "Mentions Found", count: total_mentions, percentage: 100 },
        { name: "Mentions Analyzed", count: analyzed_mentions, percentage: safe_percentage(analyzed_mentions, total_mentions) },
        { name: "Leads Qualified", count: qualified_leads, percentage: safe_percentage(qualified_leads, total_mentions) },
        { name: "Leads Contacted", count: contacted_leads, percentage: safe_percentage(contacted_leads, total_mentions) }
      ]
    }
  end

  def fetch_top_performers
    {
      keywords: top_keywords,
      integrations: top_integrations,
      leads: recent_high_score_leads,
      conversion_sources: top_conversion_sources
    }
  end

  def default_top_performers
    {
      keywords: [],
      integrations: [],
      leads: [],
      conversion_sources: []
    }
  end

  def top_keywords
    user.keywords
        .joins(:leads)
        .where(leads: { created_at: date_range })
        .group("keywords.id", "keywords.keyword")
        .order("COUNT(leads.id) DESC")
        .limit(5)
        .pluck("keywords.keyword", "COUNT(leads.id) as lead_count")
        .map { |k, c| { keyword: k, lead_count: c } }
  end

  def top_integrations
    user.integrations
        .active
        .limit(5)
        .map { |i| { name: i.platform, status: i.status } }
  end

  def recent_high_score_leads
    user.leads
        .where(created_at: date_range)
        .where("score >= ?", 70)
        .order(score: :desc, created_at: :desc)
        .limit(5)
        .includes(:keyword, :mention)
        .map do |lead|
          {
            id: lead.id,
            author_name: lead.author_name,
            score: lead.score,
            keyword: lead.keyword&.keyword,
            platform: lead.mention&.platform
          }
        end
  end

  def top_conversion_sources
    user.mentions
        .joins(:lead)
        .where(created_at: date_range)
        .group(:platform)
        .count
        .sort_by { |_, count| -count }
        .first(5)
        .map { |platform, count| { platform: platform, count: count } }
  end

  def calculate_conversion_rate
    total_mentions = user.mentions.where(created_at: date_range).count
    total_leads = user.leads.where(created_at: date_range).count
    safe_percentage(total_leads, total_mentions)
  end

  def calculate_average_score
    user.leads.where(created_at: date_range).average(:score)&.round(1) || 0
  end

  def calculate_avg_response_time
    leads_with_mentions = user.leads
                              .joins(:mention)
                              .where(created_at: date_range)
                              .pluck("leads.created_at", "mentions.created_at")

    return nil if leads_with_mentions.empty?

    total_time = leads_with_mentions.sum { |lead_time, mention_time| (lead_time - mention_time) / 60.0 }
    (total_time / leads_with_mentions.count).round(1)
  end

  def calculate_engagement_rate
    total_mentions = user.mentions.where(created_at: date_range).count
    # Mentions that have leads are considered engaged
    engaged_mentions = user.mentions
                          .where(created_at: date_range)
                          .joins("LEFT JOIN leads ON leads.mention_id = mentions.id")
                          .where.not(leads: { id: nil })
                          .count
    safe_percentage(engaged_mentions, total_mentions)
  end

  def calculate_trend(metric)
    case metric
    when :mentions
      current_count = user.mentions.where(created_at: date_range).count
      previous_count = user.mentions.where(created_at: previous_period).count
    when :leads
      current_count = user.leads.where(created_at: date_range).count
      previous_count = user.leads.where(created_at: previous_period).count
    when :conversion_rate
      current_rate = calculate_conversion_rate
      previous_mentions = user.mentions.where(created_at: previous_period).count
      previous_leads = user.leads.where(created_at: previous_period).count
      previous_rate = safe_percentage(previous_leads, previous_mentions)

      return {
        current: current_rate,
        previous: previous_rate,
        change: current_rate - previous_rate,
        direction: current_rate > previous_rate ? "up" : "down"
      }
    else
      return { current: 0, previous: 0, change: 0, direction: "neutral" }
    end

    change = previous_count > 0 ? ((current_count - previous_count).to_f / previous_count * 100).round(1) : 0

    {
      current: current_count,
      previous: previous_count,
      change: change,
      direction: change > 0 ? "up" : (change < 0 ? "down" : "neutral")
    }
  end

  def date_range
    start_date..end_date
  end

  def previous_period
    duration = end_date - start_date
    (start_date - duration - 1.day)..(start_date - 1.day)
  end

  def safe_percentage(numerator, denominator)
    return 0 if denominator.nil? || denominator == 0
    (numerator.to_f / denominator * 100).round(1)
  end
end