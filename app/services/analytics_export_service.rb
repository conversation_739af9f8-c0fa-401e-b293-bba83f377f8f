# app/services/analytics_export_service.rb
require "csv"

class AnalyticsExportService
  def initialize(export_type:, data:)
    @export_type = export_type
    @data = data
  end

  def generate_csv
    CSV.generate(headers: true) do |csv|
      case @export_type
      when "overview"
        generate_overview_csv(csv)
      when "keywords"
        generate_keywords_csv(csv)
      when "leads"
        generate_leads_csv(csv)
      when "performance"
        generate_performance_csv(csv)
      else
        generate_generic_csv(csv)
      end
    end
  end

  private

  def generate_overview_csv(csv)
    csv << ["Metric", "Value", "Change %", "Previous Period"]

    @data[:overview]&.each do |metric, values|
      csv << [
        sanitize_csv_value(metric.to_s.humanize),
        sanitize_csv_value(values[:current]),
        sanitize_csv_value(values[:change_percentage]),
        sanitize_csv_value(values[:previous])
      ]
    end

    # Add top keywords if present
    if @data[:top_keywords]
      csv << []
      csv << ["Top Keywords", "Mention Count"]
      @data[:top_keywords]&.each do |keyword, count|
        csv << [
          sanitize_csv_value(keyword),
          sanitize_csv_value(count)
        ]
      end
    end
  end

  def generate_keywords_csv(csv)
    csv << ["Keyword", "Platform", "Mentions", "Leads", "Conversion Rate", "Avg Score"]

    @data[:keywords]&.each do |keyword|
      csv << [
        sanitize_csv_value(keyword[:keyword]),
        sanitize_csv_value(keyword[:platform]),
        sanitize_csv_value(keyword[:mentions_count]),
        sanitize_csv_value(keyword[:leads_count]),
        sanitize_csv_value("#{keyword[:conversion_rate]}%"),
        sanitize_csv_value(keyword[:average_score])
      ]
    end
  end

  def generate_leads_csv(csv)
    csv << ["ID", "Name", "Email", "Company", "Score", "Status", "Source", "Created At"]

    @data[:leads]&.each do |lead|
      csv << [
        sanitize_csv_value(lead.id),
        sanitize_csv_value(lead.author_name),
        sanitize_csv_value(lead.email),
        sanitize_csv_value(lead.company),
        sanitize_csv_value(lead.score),
        sanitize_csv_value(lead.status),
        sanitize_csv_value(lead.mention&.platform),
        sanitize_csv_value(format_datetime(lead.created_at))
      ]
    end
  end

  def generate_performance_csv(csv)
    csv << ["Performance Metrics"]
    csv << ["Metric", "Value"]

    @data[:performance]&.each do |metric, value|
      csv << [
        sanitize_csv_value(metric.to_s.humanize),
        sanitize_csv_value(value)
      ]
    end

    # Add time series data if present
    if @data[:time_series]
      csv << []
      csv << ["Time Series Data"]
      csv << ["Date", "Value"]
      @data[:time_series]&.each do |date, value|
        csv << [
          sanitize_csv_value(format_date(date)),
          sanitize_csv_value(value)
        ]
      end
    end

    # Add comparative metrics if present
    if @data[:comparative]
      csv << []
      csv << ["Comparative Analysis"]
      csv << ["Period", "Mentions", "Leads", "Conversion Rate"]

      csv << [
        "Current",
        sanitize_csv_value(@data[:comparative][:current][:mentions]),
        sanitize_csv_value(@data[:comparative][:current][:leads]),
        sanitize_csv_value("#{@data[:comparative][:current][:conversion_rate]}%")
      ]

      csv << [
        "Previous",
        sanitize_csv_value(@data[:comparative][:previous][:mentions]),
        sanitize_csv_value(@data[:comparative][:previous][:leads]),
        sanitize_csv_value("#{@data[:comparative][:previous][:conversion_rate]}%")
      ]

      csv << [
        "Change",
        sanitize_csv_value("#{@data[:comparative][:change][:mentions]}%"),
        sanitize_csv_value("#{@data[:comparative][:change][:leads]}%"),
        sanitize_csv_value("#{@data[:comparative][:change][:conversion_rate]} pp")
      ]
    end
  end

  def generate_generic_csv(csv)
    if @data.is_a?(Hash)
      # Export each hash key as a section
      @data.each do |section, section_data|
        next unless section_data.is_a?(Array) || section_data.is_a?(Hash)

        csv << [sanitize_csv_value(section.to_s.humanize)]

        if section_data.is_a?(Array) && section_data.first.is_a?(Hash)
          # Array of hashes
          csv << section_data.first.keys.map { |k| sanitize_csv_value(k.to_s.humanize) }
          section_data.each do |row|
            csv << row.values.map { |v| sanitize_csv_value(v) }
          end
        elsif section_data.is_a?(Hash)
          # Single hash
          csv << ["Metric", "Value"]
          section_data.each do |key, value|
            csv << [sanitize_csv_value(key.to_s.humanize), sanitize_csv_value(value)]
          end
        end

        csv << [] # Empty row between sections
      end
    elsif @data.is_a?(Array) && @data.first.is_a?(Hash)
      # Direct array of hashes
      csv << @data.first.keys.map { |k| sanitize_csv_value(k.to_s.humanize) }
      @data.each do |row|
        csv << row.values.map { |v| sanitize_csv_value(v) }
      end
    end
  end

  # Sanitize CSV values to prevent injection attacks
  def sanitize_csv_value(value)
    return "" if value.nil?

    # Convert to string
    str_value = value.to_s

    # Escape formula injection characters
    # If the value starts with =, +, -, @, or tab, prefix with single quote
    if str_value.match?(/\A[=+\-@\t\r]/)
      "'#{str_value}"
    else
      str_value
    end
  end

  def format_datetime(datetime)
    return "" unless datetime
    datetime.strftime("%Y-%m-%d %H:%M")
  end

  def format_date(date)
    return "" unless date

    if date.respond_to?(:strftime)
      date.strftime("%Y-%m-%d")
    else
      date.to_s
    end
  end
end