# app/services/exporters/keywords_exporter.rb
class KeywordsExporter
  def initialize(user:, date_range:)
    @user = user
    @date_range = date_range
  end

  def export
    {
      keywords: keywords_data
    }
  end

  private

  def keywords_data
    @user.keywords.map do |keyword|
      build_keyword_data(keyword)
    end
  end

  def build_keyword_data(keyword)
    mentions_count = keyword.mentions.where(created_at: @date_range).count
    leads_count = keyword.leads.where(created_at: @date_range).count

    {
      keyword: keyword.keyword,
      platform: keyword.platform,
      mentions_count: mentions_count,
      leads_count: leads_count,
      conversion_rate: calculate_conversion_rate(mentions_count, leads_count),
      average_score: calculate_average_score(keyword)
    }
  end

  def calculate_conversion_rate(mentions_count, leads_count)
    return 0 if mentions_count == 0

    (leads_count.to_f / mentions_count * 100).round(2)
  end

  def calculate_average_score(keyword)
    keyword.leads
           .where(created_at: @date_range)
           .average(:score)&.round(2) || 0
  end
end