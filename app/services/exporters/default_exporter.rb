# app/services/exporters/default_exporter.rb
class DefaultExporter
  def initialize(user:, date_range:)
    @user = user
    @date_range = date_range
  end

  def export
    {
      overview: overview_metrics,
      performance: performance_data
    }
  end

  private

  def overview_metrics
    {
      total_mentions: @user.mentions.where(created_at: @date_range).count,
      total_leads: @user.leads.where(created_at: @date_range).count,
      total_keywords: @user.keywords.count,
      active_integrations: @user.integrations.active.count
    }
  end

  def performance_data
    mentions = @user.mentions.where(created_at: @date_range).count
    leads = @user.leads.where(created_at: @date_range).count

    {
      conversion_rate: mentions > 0 ? (leads.to_f / mentions * 100).round(2) : 0,
      average_score: @user.leads.where(created_at: @date_range).average(:score)&.round(2) || 0
    }
  end
end