# app/services/exporters/performance_exporter.rb
class PerformanceExporter
  def initialize(user:, date_range:)
    @user = user
    @date_range = date_range
  end

  def export
    {
      performance: performance_data,
      time_series: time_series_data,
      comparative: comparative_metrics
    }
  end

  private

  def performance_data
    {
      total_mentions: total_mentions,
      total_leads: total_leads,
      conversion_rate: conversion_rate,
      average_score: average_score,
      response_rate: response_rate
    }
  end

  def time_series_data
    # Group data by day for the date range
    @user.mentions
         .where(created_at: @date_range)
         .group_by_day(:created_at)
         .count
  end

  def comparative_metrics
    current_period_metrics = calculate_period_metrics(@date_range)
    previous_range = calculate_previous_range
    previous_period_metrics = calculate_period_metrics(previous_range)

    {
      current: current_period_metrics,
      previous: previous_period_metrics,
      change: calculate_change(current_period_metrics, previous_period_metrics)
    }
  end

  def total_mentions
    @total_mentions ||= @user.mentions.where(created_at: @date_range).count
  end

  def total_leads
    @total_leads ||= @user.leads.where(created_at: @date_range).count
  end

  def conversion_rate
    return 0 if total_mentions == 0

    (total_leads.to_f / total_mentions * 100).round(2)
  end

  def average_score
    @user.leads.where(created_at: @date_range).average(:score)&.round(2) || 0
  end

  def response_rate
    responded = @user.leads.where(created_at: @date_range, status: "responded").count
    return 0 if total_leads == 0

    (responded.to_f / total_leads * 100).round(2)
  end

  def calculate_period_metrics(range)
    mentions = @user.mentions.where(created_at: range).count
    leads = @user.leads.where(created_at: range).count

    {
      mentions: mentions,
      leads: leads,
      conversion_rate: mentions > 0 ? (leads.to_f / mentions * 100).round(2) : 0
    }
  end

  def calculate_previous_range
    duration = @date_range.end - @date_range.begin
    (@date_range.begin - duration)..@date_range.begin
  end

  def calculate_change(current, previous)
    {
      mentions: calculate_percentage_change(current[:mentions], previous[:mentions]),
      leads: calculate_percentage_change(current[:leads], previous[:leads]),
      conversion_rate: current[:conversion_rate] - previous[:conversion_rate]
    }
  end

  def calculate_percentage_change(current, previous)
    return 0 if previous == 0

    ((current - previous).to_f / previous * 100).round(2)
  end
end