# app/services/exporters/overview_exporter.rb
class OverviewExporter
  def initialize(user:, date_range:)
    @user = user
    @date_range = date_range
  end

  def export
    {
      overview: overview_metrics,
      performance: performance_data,
      conversion_funnel: conversion_funnel,
      top_keywords: top_keywords
    }
  end

  private

  def overview_metrics
    # This would call the controller's calculate_overview_metrics method
    # or implement the logic here
    {
      total_mentions: @user.mentions.where(created_at: @date_range).count,
      total_leads: @user.leads.where(created_at: @date_range).count,
      total_keywords: @user.keywords.count
    }
  end

  def performance_data
    # This would call the controller's fetch_performance_data method
    # or implement the logic here
    {
      conversion_rate: calculate_conversion_rate,
      average_score: calculate_average_score
    }
  end

  def conversion_funnel
    # This would call the controller's calculate_conversion_funnel method
    # or implement the logic here
    total_mentions = @user.mentions.where(created_at: @date_range).count
    total_leads = @user.leads.where(created_at: @date_range).count

    {
      mentions: total_mentions,
      leads: total_leads,
      conversion_rate: total_mentions > 0 ? (total_leads.to_f / total_mentions * 100).round(2) : 0
    }
  end

  def top_keywords
    @user.keywords
         .joins(:mentions)
         .where(mentions: { created_at: @date_range })
         .group("keywords.id", "keywords.keyword")
         .order("COUNT(mentions.id) DESC")
         .limit(10)
         .pluck("keywords.keyword", Arel.sql("COUNT(mentions.id)"))
  end

  def calculate_conversion_rate
    total_mentions = @user.mentions.where(created_at: @date_range).count
    total_leads = @user.leads.where(created_at: @date_range).count
    return 0 if total_mentions == 0

    (total_leads.to_f / total_mentions * 100).round(2)
  end

  def calculate_average_score
    @user.leads.where(created_at: @date_range).average(:score)&.round(2) || 0
  end
end