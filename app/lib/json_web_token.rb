# app/lib/json_web_token.rb
class JsonWebToken
  class << self
    def encode(payload, exp = 24.hours.from_now)
      payload[:exp] = exp.to_i
      JWT.encode(payload, jwt_secret, 'HS256')
    end

    def decode(token)
      body = JWT.decode(token, jwt_secret, true, algorithm: 'HS256')[0]
      HashWithIndifferentAccess.new body
    rescue JWT::DecodeError => e
      Rails.logger.error "JWT Decode Error: #{e.message}"
      nil
    rescue JWT::ExpiredSignature
      Rails.logger.error "JWT Token Expired"
      nil
    end

    private

    def jwt_secret
      Rails.application.credentials.jwt_secret || raise("JWT secret not configured!")
    end
  end
end