# frozen_string_literal: true

module Agents
  module CodeReview
    # Container for code review results
    class ReviewResults
      attr_reader :issues, :stats, :errors, :warnings, :suggestions

      def initialize
        @issues = []
        @stats = {
          total_files: 0,
          files_reviewed: 0,
          issues_found: 0,
          critical_issues: 0,
          high_issues: 0,
          medium_issues: 0,
          low_issues: 0,
          info_issues: 0,
          fixable_issues: 0,
          fixed_issues: 0
        }
        @errors = []
        @warnings = []
        @suggestions = []
      end

      def add_issue(issue)
        @issues << Issue.new(issue) unless issue.is_a?(Issue)
        @issues << issue if issue.is_a?(Issue)
        update_stats
      end

      def add_error(error)
        @errors << error
      end

      def add_warning(warning)
        @warnings << warning
      end

      def add_suggestion(suggestion)
        @suggestions << suggestion
      end

      def merge!(other_results)
        return unless other_results

        @issues.concat(other_results.issues) if other_results.respond_to?(:issues)
        @errors.concat(other_results.errors) if other_results.respond_to?(:errors)
        @warnings.concat(other_results.warnings) if other_results.respond_to?(:warnings)
        @suggestions.concat(other_results.suggestions) if other_results.respond_to?(:suggestions)
        
        update_stats
      end

      def aggregate!
        # Group issues by file and type
        grouped = @issues.group_by { |i| [i.file_path, i.category] }
        
        # Merge similar issues
        @issues = grouped.flat_map do |(_file, category), issues|
          merge_similar_issues(issues)
        end
      end

      def prioritize!
        @issues.sort_by! { |issue| [-issue.severity_score, issue.file_path, issue.line] }
      end

      def deduplicate!
        @issues.uniq! { |issue| issue.fingerprint }
      end

      def apply_threshold(threshold)
        threshold_value = CodeReviewAgent::SEVERITY_LEVELS[threshold] || 2
        @issues.select! { |issue| issue.severity_score >= threshold_value }
        update_stats
      end

      def passed?
        critical_count == 0 && high_count == 0
      end

      def failed?
        !passed?
      end

      def critical_count
        @issues.count { |i| i.severity == :critical }
      end

      def high_count
        @issues.count { |i| i.severity == :high }
      end

      def medium_count
        @issues.count { |i| i.severity == :medium }
      end

      def low_count
        @issues.count { |i| i.severity == :low }
      end

      def info_count
        @issues.count { |i| i.severity == :info }
      end

      def fixable_count
        @issues.count(&:fixable?)
      end

      def by_category
        @issues.group_by(&:category)
      end

      def by_file
        @issues.group_by(&:file_path)
      end

      def by_severity
        @issues.group_by(&:severity)
      end

      def summary
        {
          total_issues: @issues.size,
          critical: critical_count,
          high: high_count,
          medium: medium_count,
          low: low_count,
          info: info_count,
          fixable: fixable_count,
          passed: passed?
        }
      end

      def to_json(*_args)
        {
          summary: summary,
          issues: @issues.map(&:to_h),
          errors: @errors,
          warnings: @warnings,
          suggestions: @suggestions,
          stats: @stats
        }.to_json
      end

      private

      def update_stats
        @stats[:issues_found] = @issues.size
        @stats[:critical_issues] = critical_count
        @stats[:high_issues] = high_count
        @stats[:medium_issues] = medium_count
        @stats[:low_issues] = low_count
        @stats[:info_issues] = info_count
        @stats[:fixable_issues] = fixable_count
      end

      def merge_similar_issues(issues)
        return issues if issues.size <= 1

        # Group by similarity
        grouped = issues.group_by(&:similarity_key)
        
        grouped.map do |_key, similar_issues|
          if similar_issues.size > 1
            merge_issue_group(similar_issues)
          else
            similar_issues.first
          end
        end
      end

      def merge_issue_group(issues)
        # Take the highest severity issue as the base
        base_issue = issues.max_by(&:severity_score)
        
        # Add occurrence count
        base_issue.occurrence_count = issues.size
        
        # Merge line numbers
        base_issue.additional_lines = issues.flat_map(&:line).uniq.sort - [base_issue.line]
        
        base_issue
      end
    end

    # Individual code issue
    class Issue
      attr_accessor :file_path, :line, :column, :category, :severity, :message,
                    :rule, :suggested_fix, :occurrence_count, :additional_lines,
                    :context, :documentation_url

      def initialize(attrs = {})
        attrs.each do |key, value|
          send("#{key}=", value) if respond_to?("#{key}=")
        end
        
        @occurrence_count ||= 1
        @additional_lines ||= []
      end

      def severity_score
        CodeReviewAgent::SEVERITY_LEVELS[severity] || 0
      end

      def fixable?
        !suggested_fix.nil?
      end

      def fingerprint
        Digest::SHA256.hexdigest("#{file_path}:#{category}:#{rule}:#{message}")
      end

      def similarity_key
        "#{category}:#{rule}:#{message[0..50]}"
      end

      def to_h
        {
          file_path: file_path,
          line: line,
          column: column,
          category: category,
          severity: severity,
          message: message,
          rule: rule,
          suggested_fix: suggested_fix,
          occurrence_count: occurrence_count,
          additional_lines: additional_lines,
          context: context,
          documentation_url: documentation_url
        }.compact
      end
    end
  end
end