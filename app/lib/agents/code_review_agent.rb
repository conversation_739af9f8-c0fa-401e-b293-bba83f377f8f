# frozen_string_literal: true

module Agents
  # Comprehensive Code Review Agent
  # Performs multi-dimensional code analysis including security, performance,
  # quality, testing, and compliance checks across multiple languages
  class CodeReviewAgent
    include ActiveSupport::Configurable

    # Review severity levels
    SEVERITY_LEVELS = {
      critical: 4,
      high: 3,
      medium: 2,
      low: 1,
      info: 0
    }.freeze

    # Review categories
    REVIEW_CATEGORIES = %i[
      architecture
      security
      performance
      quality
      testing
      style
      documentation
      accessibility
      compliance
    ].freeze

    # Language support
    SUPPORTED_LANGUAGES = %i[
      ruby rails javascript typescript python go java csharp php swift kotlin
    ].freeze

    config_accessor :severity_threshold, default: :medium
    config_accessor :auto_fix_enabled, default: false
    config_accessor :parallel_processing, default: true
    config_accessor :incremental_mode, default: true
    config_accessor :max_issues_per_file, default: 100
    config_accessor :timeout_seconds, default: 300

    attr_reader :results, :metrics, :start_time

    def initialize(options = {})
      @options = options
      @results = ReviewResults.new
      @metrics = ReviewMetrics.new
      @analyzers = []
      @start_time = nil
      
      configure_from_options(options)
      load_analyzers
    end

    # Main entry point for code review
    def review(code_path, options = {})
      @start_time = Time.current
      
      review_options = @options.merge(options)
      
      Rails.logger.info "Starting code review for: #{code_path}"
      
      begin
        # Prepare review context
        context = prepare_context(code_path, review_options)
        
        # Run all analyzers in parallel or sequence
        run_analysis(context)
        
        # Aggregate and prioritize results
        aggregate_results
        
        # Generate fixes if enabled
        generate_fixes if auto_fix_enabled
        
        # Calculate metrics
        calculate_metrics
        
        # Generate report
        generate_report(review_options[:format] || :json)
        
      rescue StandardError => e
        handle_review_error(e)
      ensure
        cleanup
      end
      
      @results
    end

    # Review a specific file
    def review_file(file_path, options = {})
      return unless File.exist?(file_path)
      
      file_context = FileContext.new(file_path, options)
      language = detect_language(file_path)
      
      applicable_analyzers = @analyzers.select { |a| a.supports?(language) }
      
      applicable_analyzers.each do |analyzer|
        analyzer.analyze(file_context)
      end
      
      file_context.results
    end

    # Review git diff/patch
    def review_diff(diff_content, options = {})
      diff_parser = DiffParser.new(diff_content)
      changed_files = diff_parser.parse
      
      changed_files.each do |file|
        next unless should_review_file?(file.path)
        
        review_file_changes(file, options)
      end
      
      @results
    end

    # Review pull request
    def review_pull_request(pr_url, options = {})
      pr_analyzer = PullRequestAnalyzer.new(pr_url, options)
      
      pr_analyzer.fetch_changes.each do |change|
        review_file(change.file_path, change.options)
      end
      
      add_pr_specific_checks(pr_analyzer)
      
      @results
    end

    private

    def configure_from_options(options)
      options.each do |key, value|
        send("#{key}=", value) if respond_to?("#{key}=")
      end
    end

    def load_analyzers
      @analyzers = [
        Analyzers::SecurityAnalyzer.new(@options),
        Analyzers::PerformanceAnalyzer.new(@options),
        Analyzers::QualityAnalyzer.new(@options),
        Analyzers::TestingAnalyzer.new(@options),
        Analyzers::StyleAnalyzer.new(@options),
        Analyzers::DocumentationAnalyzer.new(@options),
        Analyzers::ArchitectureAnalyzer.new(@options),
        Analyzers::AccessibilityAnalyzer.new(@options),
        Analyzers::ComplianceAnalyzer.new(@options)
      ]
      
      # Load language-specific analyzers
      load_language_specific_analyzers
      
      # Load framework-specific analyzers
      load_framework_specific_analyzers
    end

    def load_language_specific_analyzers
      # Ruby/Rails specific
      @analyzers << Analyzers::RubyAnalyzer.new(@options) if ruby_project?
      @analyzers << Analyzers::RailsAnalyzer.new(@options) if rails_project?
      
      # JavaScript/TypeScript specific
      @analyzers << Analyzers::JavaScriptAnalyzer.new(@options) if javascript_project?
      @analyzers << Analyzers::TypeScriptAnalyzer.new(@options) if typescript_project?
      
      # Python specific
      @analyzers << Analyzers::PythonAnalyzer.new(@options) if python_project?
    end

    def load_framework_specific_analyzers
      # React/Vue/Angular
      @analyzers << Analyzers::ReactAnalyzer.new(@options) if react_project?
      @analyzers << Analyzers::VueAnalyzer.new(@options) if vue_project?
      
      # Django/Flask
      @analyzers << Analyzers::DjangoAnalyzer.new(@options) if django_project?
    end

    def prepare_context(code_path, options)
      ReviewContext.new(
        path: code_path,
        options: options,
        project_type: detect_project_type(code_path),
        git_info: extract_git_info(code_path),
        dependencies: analyze_dependencies(code_path),
        configuration: load_project_config(code_path)
      )
    end

    def run_analysis(context)
      if parallel_processing && @analyzers.size > 1
        run_parallel_analysis(context)
      else
        run_sequential_analysis(context)
      end
    end

    def run_parallel_analysis(context)
      futures = @analyzers.map do |analyzer|
        Concurrent::Future.execute { analyzer.analyze(context) }
      end
      
      futures.each(&:wait)
      
      futures.each do |future|
        if future.fulfilled?
          merge_results(future.value)
        elsif future.rejected?
          log_analyzer_error(future.reason)
        end
      end
    end

    def run_sequential_analysis(context)
      @analyzers.each do |analyzer|
        result = analyzer.analyze(context)
        merge_results(result)
      rescue StandardError => e
        log_analyzer_error(e, analyzer)
      end
    end

    def aggregate_results
      @results.aggregate!
      @results.prioritize!
      @results.deduplicate!
      @results.apply_threshold(severity_threshold)
    end

    def generate_fixes
      return unless auto_fix_enabled
      
      @results.issues.each do |issue|
        next unless issue.fixable?
        
        fix = FixGenerator.generate(issue)
        issue.suggested_fix = fix if fix
      end
    end

    def calculate_metrics
      @metrics.calculate(
        results: @results,
        start_time: @start_time,
        end_time: Time.current
      )
    end

    def generate_report(format)
      reporter = ReportGenerator.new(@results, @metrics)
      
      case format
      when :json
        reporter.to_json
      when :html
        reporter.to_html
      when :markdown
        reporter.to_markdown
      when :github
        reporter.to_github_comment
      else
        reporter.to_json
      end
    end

    def handle_review_error(error)
      Rails.logger.error "Code review failed: #{error.message}"
      Rails.logger.error error.backtrace.join("\n")
      
      @results.add_error(
        message: error.message,
        category: :system,
        severity: :critical
      )
    end

    def cleanup
      @analyzers.each(&:cleanup) if @analyzers
    end

    def detect_language(file_path)
      extension = File.extname(file_path).delete('.')
      
      case extension
      when 'rb' then :ruby
      when 'js', 'jsx' then :javascript
      when 'ts', 'tsx' then :typescript
      when 'py' then :python
      when 'go' then :go
      when 'java' then :java
      when 'cs' then :csharp
      when 'php' then :php
      when 'swift' then :swift
      when 'kt', 'kts' then :kotlin
      else :unknown
      end
    end

    def should_review_file?(file_path)
      return false if excluded_file?(file_path)
      return false if binary_file?(file_path)
      return false if generated_file?(file_path)
      
      true
    end

    def excluded_file?(file_path)
      exclusion_patterns = @options[:exclude_patterns] || []
      exclusion_patterns.any? { |pattern| file_path.match?(pattern) }
    end

    def binary_file?(file_path)
      File.binary?(file_path) rescue false
    end

    def generated_file?(file_path)
      file_path.include?('node_modules') ||
        file_path.include?('vendor') ||
        file_path.include?('.min.') ||
        file_path.include?('dist/')
    end

    def ruby_project?
      File.exist?('Gemfile')
    end

    def rails_project?
      File.exist?('Gemfile') && File.exist?('config/application.rb')
    end

    def javascript_project?
      File.exist?('package.json')
    end

    def typescript_project?
      File.exist?('tsconfig.json')
    end

    def python_project?
      File.exist?('requirements.txt') || File.exist?('setup.py') || File.exist?('Pipfile')
    end

    def react_project?
      return false unless File.exist?('package.json')
      
      package = JSON.parse(File.read('package.json'))
      package['dependencies']&.key?('react') || package['devDependencies']&.key?('react')
    rescue
      false
    end

    def vue_project?
      return false unless File.exist?('package.json')
      
      package = JSON.parse(File.read('package.json'))
      package['dependencies']&.key?('vue') || package['devDependencies']&.key?('vue')
    rescue
      false
    end

    def django_project?
      File.exist?('manage.py') && python_project?
    end

    def detect_project_type(path)
      return :rails if rails_project?
      return :react if react_project?
      return :vue if vue_project?
      return :django if django_project?
      return :ruby if ruby_project?
      return :javascript if javascript_project?
      return :python if python_project?
      
      :unknown
    end

    def extract_git_info(path)
      return {} unless File.directory?(File.join(path, '.git'))
      
      {
        branch: `git branch --show-current`.strip,
        commit: `git rev-parse HEAD`.strip,
        author: `git config user.name`.strip,
        remote: `git remote get-url origin`.strip
      }
    rescue
      {}
    end

    def analyze_dependencies(path)
      deps = {}
      
      # Ruby dependencies
      if File.exist?(File.join(path, 'Gemfile.lock'))
        deps[:ruby] = parse_gemfile_lock(File.join(path, 'Gemfile.lock'))
      end
      
      # JavaScript dependencies
      if File.exist?(File.join(path, 'package-lock.json'))
        deps[:javascript] = parse_package_lock(File.join(path, 'package-lock.json'))
      end
      
      deps
    end

    def parse_gemfile_lock(file_path)
      # Parse Gemfile.lock for dependency analysis
      []
    end

    def parse_package_lock(file_path)
      # Parse package-lock.json for dependency analysis
      []
    end

    def load_project_config(path)
      config_file = File.join(path, '.code-review.yml')
      return {} unless File.exist?(config_file)
      
      YAML.load_file(config_file).deep_symbolize_keys
    rescue
      {}
    end

    def merge_results(analyzer_results)
      @results.merge!(analyzer_results)
    end

    def log_analyzer_error(error, analyzer = nil)
      message = analyzer ? "#{analyzer.class.name} failed: #{error.message}" : error.message
      Rails.logger.error message
    end

    def review_file_changes(file, options)
      # Review only changed lines in file
      file.hunks.each do |hunk|
        analyze_hunk(file.path, hunk, options)
      end
    end

    def analyze_hunk(file_path, hunk, options)
      # Analyze specific code changes
    end

    def add_pr_specific_checks(pr_analyzer)
      # Add pull request specific checks
      check_pr_title(pr_analyzer)
      check_pr_description(pr_analyzer)
      check_pr_size(pr_analyzer)
      check_pr_test_coverage(pr_analyzer)
    end

    def check_pr_title(pr_analyzer)
      # Validate PR title format
    end

    def check_pr_description(pr_analyzer)
      # Validate PR description completeness
    end

    def check_pr_size(pr_analyzer)
      # Check if PR is too large
    end

    def check_pr_test_coverage(pr_analyzer)
      # Verify test coverage for changes
    end
  end
end